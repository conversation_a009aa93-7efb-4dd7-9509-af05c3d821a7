import { IWorkflowState } from '../flow'
import { BaoshuWorkFlowNode, trackInvoke } from './baseNode'
import { BaoshuNode } from '../type'
import chalk from 'chalk'
import { HumanTransfer, HumanTransferType } from '../../human_transfer'
import { LLMNode } from './llm'
import { ChatStateStore, ConversationState } from '../../../storage/chat_state_store'
import { XbbHelper } from './helper/xbbHelper'
import logger from '../../../../../model/logger/logger'

/**
 * 最终的结束节点，最终所有节点都会落入这个节点
 */
export class EndNode extends BaoshuWorkFlowNode {
  @trackInvoke
  public static async invoke(state: IWorkflowState) {
    const invokeCount = ChatStateStore.get(state.chat_id).nodeInvokeCount[EndNode.name]
    const status = ChatStateStore.getStatus(state.chat_id)

    if (invokeCount === 0) { // 结束节点
      try {
        await XbbHelper.labelClue(state.chat_id, state.user_id)
      } catch (e) {
        console.error(e)
        logger.error('延时更新标签失败', e)
      }
    }

    if (invokeCount <= 2) {
      // 礼貌结束对话
      if (status.consultGroupEntered) {
        await LLMNode.invoke({
          state,
          dynamicPrompt: '当前客户已经拉到群里了，可以以有什么问题可以问群内老师来结束对话',
        })
      } else {
        await LLMNode.invoke({
          state,
          dynamicPrompt: `当前客户不是目标客户，需要礼貌的结束对话，但是我们希望让客户整体感受更好一些的，所以如果客户没有进一步的问题，礼貌鼓励下客户即退出对话。不要承诺给他规划，或找老师来咨询。
如果客户客户还有进一步问题，简单回复结束当前话题后便退出对话。不要再延展对话。
可以 follow 以下让客户没有进一步追问的技巧
-在个性化问题上，没有绝对对错的问题上，赞同他和鼓励他的选择。例如：“可以的”，“挺好的，加油”
-延续前文对话方向（多存存钱，体制内问题不是很熟悉等），引导客户在这些路径上努力：“预算问题先搞定，出国是硬门槛的，加油！”，“体制内我确实不大熟悉，就不给意见了。多问问学长学姐之类。”。`,
        })
      }

    } else {
      logger.log(chalk.greenBright('结束对话'))
      await HumanTransfer.transfer(state.chat_id, state.user_id, HumanTransferType.End)
    }

    ChatStateStore.update(state.chat_id, {
      state: ConversationState.End
    })

    return BaoshuNode.END
  }
}