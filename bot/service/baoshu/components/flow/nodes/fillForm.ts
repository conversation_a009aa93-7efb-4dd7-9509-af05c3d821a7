import { trackInvoke } from './baseNode'
import { IWorkflowState } from '../flow'
import { ChatStateStore, ConversationState } from '../../../storage/chat_state_store'
import { BaoshuNode } from '../type'
import { WechatMessageSender } from '../../message_send'
import { IntentionCalculateNode } from './intentionCalculate'
import { randomSleep, sleep } from '../../../../../lib/schedule/schedule'
import { LLMNode } from './llm'
import { IWecomMsgType } from '../../../../../lib/juzi/type'
import { InviteToConsultantNode } from './inviteToGroup'

export class FillFormNode {
  @trackInvoke
  public static async invoke(state: IWorkflowState) {
    const nodeInvokeCount = ChatStateStore.get(state.chat_id).nodeInvokeCount[FillFormNode.name]

    if (nodeInvokeCount === 0) {
      await WechatMessageSender.sendById({
        chat_id: state.chat_id,
        user_id: state.user_id,
        ai_msg: '因为这2天高考出分，实在是太忙了。提高下效率，把你的信息帮我规整下，我来高效看下'
      })

      await sleep(2000)

      await WechatMessageSender.sendById({
        chat_id: state.chat_id,
        user_id: state.user_id,
        ai_msg: `请先填下这个表：
姓名：
电话：
年级：
高考总分：
英语成绩：
所在城市：
意向国家/地区：
本科总预算：
意向专业（文商科/理工科/艺术类）：

快速填写知道的部分就可以，不清楚的跳过，不过信息越全面，方案越准确～`
      })

      await randomSleep(1000, 3000)

      await WechatMessageSender.sendById({
        chat_id: state.chat_id,
        user_id: state.user_id,
        ai_msg: '😊',
        send_msg: {
          type: IWecomMsgType.Emoticon,
          imageUrl: 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/a36d7270254a788cb8753434fbff233e.gif'
        },
      })

      ChatStateStore.update(state.chat_id, {
        status: {
          is_send_form: true
        }
      })

      return BaoshuNode.FillFormNode
    }

    // extractUserSlots
    await IntentionCalculateNode.getQuerySlot(state.chat_id, state.user_id, state.round_id)


    // 判断表是否填完
    if (await this.isFormFilled(state.chat_id)) {
      //拉群
      return await InviteToConsultantNode.invoke(state)
    }

    if (nodeInvokeCount >= 2) {
      return await IntentionCalculateNode.invoke(state)
    }

    await LLMNode.invoke({
      state,
      dynamicPrompt: `Your task is to guide the user through filling out the form fields and provide relevant information when needed.

  Follow these rules when helping the user fill out the form:
  1. For language test scores, if the user studied English in high school, ask for English test scores. If they studied Japanese, ask for Japanese test scores.
  2. If the user doesn't have a clear idea about their budget, provide information about typical study abroad budgets. Use this template:
  "一般主流国家，美国每年60-80w, 英国要准备每年40-50w，新加坡每年25-35w，国际本科平均每年20w左右，总共40-80w，性价比高的韩国/马来西亚，每年10-15w差不多就可以的。 看看大概能卡在哪个档呢，出去预算还是一个很硬的门槛的。"
  
  3. If the user doesn't respond to a specific field or asks an unrelated question, gently guide them back to filling out the form. You can say: "高考季叔实在非常忙，规整下表格信息我，比较高效。"
  4. Be polite and patient throughout the conversation.`
    })


    return BaoshuNode.FillFormNode
  }

  private static async isFormFilled(chat_id: string) {
    const userSlots = ChatStateStore.get(chat_id).userSlots
    let slotsNumber = 0
    if(userSlots?.name) {
      slotsNumber += 1
    }
    if(userSlots?.phone) {
      slotsNumber += 1
    }
    if(userSlots?.grade) {
      slotsNumber += 1
    }
    if(userSlots?.language_test_score) {
      slotsNumber += 1
    }
    if(userSlots?.city) {
      slotsNumber += 1
    }
    if(userSlots?.user_intended_country) {
      slotsNumber += 1
    }
    if(userSlots?.budget) {
      slotsNumber += 1
    }
    if(userSlots?.major) {
      slotsNumber += 1
    }
    if (userSlots?.preferred_plan) {
      slotsNumber += 1
    }

    if(userSlots?.budget && userSlots?.budget >= 30 && slotsNumber >= 3) {
      return true
    }
    if(userSlots?.name && userSlots.phone  && userSlots?.budget && userSlots?.budget >= 10 && slotsNumber >= 3) {
      return true
    }
  }
}