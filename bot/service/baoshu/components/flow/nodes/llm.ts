import { IWorkflowState } from '../flow'
import { LLM } from '../../../../../lib/ai/llm/LLM'
import { ChatHistoryService } from '../../chat_history'
import { HumanMessage, SystemMessage } from '@langchain/core/messages'
import { IterableReadableStream } from '@langchain/core/dist/utils/stream'
import { HashMessagesHandler } from '../../interrupt_handler'
import { CommonRegexType, RegexHelper } from '../../../../../lib/regex/regex'
import { Config } from '../../../../../config/config'
import { WechatMessageSender } from '../../message_send'
import { sleep } from '../../../../../lib/schedule/schedule'
import { ChatDB } from '../../../database/chat'
import logger from '../../../../../model/logger/logger'
import { ChatStateStore } from '../../../storage/chat_state_store'
import { StringHelper } from '../../../../../lib/string'
import ElasticSearchService from '../../../../../model/elastic_search/elastic_search'
import removeMarkdown from 'markdown-to-text'
import { DateHelper } from '../../../../../lib/date/date'

interface IStreamReplyParams {
    chat_id: string
    user_id: string
    stream: IterableReadableStream<string>
    interruptHandler?: HashMessagesHandler
    lineHandler: (line:string, isFirstSentence: boolean) => Promise<string>
}

export interface LLMInvokeParam {
    state: IWorkflowState
    customPrompt?: string // 自定义 Prompt 直接覆盖 SystemPrompt
    dynamicPrompt?: string // 作为 SystemPrompt 的 Task 部分进行插入
    useWebSearch?: boolean
    referenceChatHistory?: boolean
    checkRepeat?: boolean
    noInterrupt?: boolean // 不会被打断
    promptName?: string // 用于记录当前的 prompt 名称
    regenerate?: boolean // 当输出重复导致所有输出被舍弃的时候，是否要重新生成文本
    temperature?: number
}


export class LLMNode {
  /**
   * 调用 LLM， 这里只是基础 暴叔的人设，可以通过 dynamicPrompt 来动态添加提示
   * @param param
   */
  public static async invoke(param: LLMInvokeParam) {
    logger.debug({ chat_id: param.state.chat_id }, 'enter LLMNode', JSON.stringify(param, null, 2)) // 输出进入节点的信息

    // 如果当前有新消息，把当前回复丢弃掉
    if (param.state.interruptHandler && !param.noInterrupt) { // 调用模型前检查一下
      await param.state.interruptHandler.interruptProcess(param.state.chat_id)
    }

    const res = await this.llmReply(param, new LLM({
      meta: {
        chat_id: param.state.chat_id,
        round_id: param.state.round_id,
        promptName: param.promptName,
        dynamicPrompt: param.dynamicPrompt,
      },
      temperature: param.temperature
    }))

    if (!res.reply) {
      logger.warn({ chat_id: param.state.chat_id }, `信息：“${param.state.userMessage} ” 没有回复`)
    }

    if (param.regenerate && !res.reply && !res.repeatedSentence) { // 空回复 + 重复的是哪句话
      if (param.dynamicPrompt) {
        param.dynamicPrompt += `**特别注意：不要重复回复"${res.repeatedSentence}"**`
      } else if (param.customPrompt) {
        param.customPrompt += `**特别注意：不要重复回复"${res.repeatedSentence}"**`
      }
      param.referenceChatHistory = true // 设置为任务遵循模式

      const regeneratedRes = await this.llmReply(param, new LLM({
        meta: {
          chat_id: param.state.chat_id,
          round_id: param.state.round_id,
          promptName: param.promptName,
          dynamicPrompt: param.dynamicPrompt,
        }
      }))

      return regeneratedRes.reply
    }

    return res.reply
  }

  public static async llmReply(param: LLMInvokeParam, llm: LLM) {
    const stream = await this.getReplyStream(param, llm)

    // 注意重复的时候，一次重复，丢弃掉剩下所有的回复
    let hasRepeated = false
    let repeatedSentence = ''

    const reply =  await this.streamReply({
      chat_id: param.state.chat_id,
      user_id: param.state.user_id,
      stream,
      lineHandler: async (line: string, isFirstSentence) => {
        // 如果当前有新消息，把当前回复丢弃掉
        if (param.state.interruptHandler && !param.noInterrupt) {
          await param.state.interruptHandler.interruptProcess(param.state.chat_id)
        }

        if (await ChatDB.isHumanInvolvement(param.state.chat_id)) {
          logger.trace('人工介入')
          return ''
        }

        // 假设人的平均打字速度为每秒 3 个字符
        const typingSpeed = 1.7
        // 计算当前消息的字数
        const charCount = line.length

        // 计算需要等待的时间 (毫秒)
        const delayTime = Math.floor(charCount / typingSpeed * 1000)

        if (!isFirstSentence && !Config.setting.localTest) {
          await sleep(delayTime)
        }

        if (param.checkRepeat) {
          if (hasRepeated) {
            repeatedSentence = line
            logger.warn({ chat_id: param.state.chat_id }, `丢弃重复回复后的内容: ${line}`)
            return ''
          }
        }

        const isRepeated = await ChatHistoryService.isRepeatedMsg(param.state.chat_id, line)
        if (isRepeated) {
          hasRepeated = true
          logger.warn({ chat_id: param.state.chat_id }, `重复回复已丢弃: ${line}`)
          return ''
        }

        await WechatMessageSender.sendById({
          chat_id: param.state.chat_id,
          user_id: param.state.user_id,
          ai_msg: line
        })

        return line
      }
    })

    return {
      reply,
      repeatedSentence
    }
  }

  /**
   * 流式回复，将输入流分割成句子，并逐句处理
   * @param params
  */
  public static async streamReply (params: IStreamReplyParams) {
    let reply = ''
    let currentSentence = ''
    let isFirstSentence = true

    for await (const chunk of params.stream) {
      currentSentence += chunk.toString ()

      while ((!RegexHelper.strIncludeRegex (currentSentence, CommonRegexType.URL)) && currentSentence.search (/[!?~。！？\n]/) !== -1 && !currentSentence.includes('：')) {
        const [sentence, remaining] = this.extractSentence (currentSentence)
        if (sentence) {
          await params.interruptHandler?.interruptProcess (params.chat_id)
          const processedSentence = await this.sentencePostProcess (sentence)
          const replySentence = await params.lineHandler(processedSentence, isFirstSentence)
          if (replySentence) {
            reply += `${replySentence}\n`
          }

          if (isFirstSentence) {
            isFirstSentence = false
          }
        }
        currentSentence = remaining
      }

    }

    if (currentSentence.trim ()) {
      await params.interruptHandler?.interruptProcess (params.chat_id)
      const processedSentence = await this.sentencePostProcess (currentSentence)
      reply += processedSentence
      await params.lineHandler(processedSentence, isFirstSentence)
    }

    return reply
  }

  private static extractSentence (text: string): [string, string] {
    const index = text.search (/[!?~。！？\n]/)
    if (index !== -1) {
      return [text.substring (0, index + 1).trim (), text.substring (index + 1)]
    }
    return [text.trim (), '']
  }

  private static async sentencePostProcess(sentence: string) {
    sentence = removeMarkdown(sentence)
    sentence = sentence.replaceAll(`${Config.setting.BOT_NAME}:`, '').replaceAll('**', '').replaceAll('用户:', '').replace('。', ' ').replace(/\[\[[^\]]*]]/g, '').trim()
    sentence = RegexHelper.sayChinese(sentence)

    return sentence
  }

  // 构建消息流
  private static async buildMessageStream(param: LLMInvokeParam, llm: LLM): Promise<any> {
    if (param.customPrompt) {
      return await llm.stream([
        new SystemMessage(this.cleanPrompt(param.customPrompt)),
        ...(await ChatHistoryService.getLLMChatHistory(param.state.chat_id)),
      ])
    }

    if (param.referenceChatHistory && param.dynamicPrompt) { // 任务导向, 聊天记录放到 System Prompt 中
      const prompt = await this.buildPrompt(param, true)
      return await llm.stream([
        new SystemMessage(this.cleanPrompt(prompt)),
        new HumanMessage(`补全暴叔的回复\n${Config.setting.BOT_NAME}: `),
      ])
    }

    const prompt = await this.buildPrompt(param)
    return await llm.stream([
      new SystemMessage(this.cleanPrompt(prompt)),
      ...(await ChatHistoryService.getLLMChatHistory(param.state.chat_id)),
    ])
  }

  private static cleanPrompt(prompt: string) {
    return StringHelper.replaceMultipleBlankLines(prompt).trim()
  }

  private static async getReplyStream(param: LLMInvokeParam, llm: LLM) {
    return await this.buildMessageStream(param, llm)
  }

  private static async buildPrompt(param: LLMInvokeParam, referenceChatHistory = false) {
    const userDescription = ChatStateStore.getDescription(param.state.chat_id)
    const city = ChatStateStore.get(param.state.chat_id)?.userSlots?.city
    const avatar = this.getAvatar()
    const rules = this.getRules()
    const experience = await this.getExperience(userDescription, city)

    const redEnvelope = `可以在当前对话领取红包封面。给我发关键词：红包，就会发红包二维码。当前时间为 ${DateHelper.getFormattedDate(new Date(), true)}`

    if (referenceChatHistory) {
      const history = await ChatHistoryService.formatHistory(param.state.chat_id)
      return `${avatar}

${rules}

${experience}
${redEnvelope}

# You should reference the following chat history and complete the task I give you, this is very important for my career!
Chat History:
"${history}"

# TASK:
${param.dynamicPrompt || ''}

You must complete the task. Just reply as "暴叔" and continue the conversation. Do not repeat yourself.`
    } else {
      return `${avatar}

${rules}

${this.getToneStyle()}

${experience}
${redEnvelope}

# TASK:
${param.dynamicPrompt || ''}

Take a Deep Breath and Carefully Follow the Rules, Guides and Examples I gave you. I will tip you $2000 if you do EVERYTHING Perfectly.`
    }
  }

  // 获取 avatar 文本
  public static getAvatar(): string {
    return `你是一个专业的留学顾问名字叫暴叔，在B站和抖音上很知名的留学KOL，非常熟悉中国体制外以及国外教育的情况，以不论学生学习成绩多差都有人生出路为核心思想，为各类专科生；考不上普通高中学生来规划出路，打破升学焦虑。其金句“跟上暴叔的节奏，稳了！”给了焦虑的学生和家长很多希望。
暴叔在杭州经营一家留学中介公司，为有需要的同学提供申请服务，或者帮助对接经过筛选的语言培训（雅思托福等），A LEVEL,绩点管理，香港优才申请等第三方机构，暴叔在辅导规划过程中不仅擅长发现大家情绪需求，及时进行情绪价值提供。还比较强势输出个人观点；例如基于自己的判断斩钉截铁告知客户：“初中成绩不好的，家里能支持得预算也不多的时候，先都别想啦，死磕把成绩搞上去，再说，都还来得及。读上高中再来找叔规划”, "考研、考公、考编太卷了", "选择总是有的，路总是有的"等，对话中敢于为客户做决策。
相对于其他中介而言，暴叔一直觉得自己在做的是一家教育规划公司，帮助在中国越来越卷的教育环境中，根据学生的情况提供更差异化教育路线。总是能给到客户希望，"选择总是有的，路总是有的"
现在正在跟用户在企业微信进行聊天，你的咨询是免费的，你的咨询范围是中国体制外教育，注意不要给体制内择校任何建议。
不在经营范围的国家有：菲律宾，斯里兰卡，外蒙古，用户如果主动问及这些国家，表示我们不做这些国家的规划，可以表示不熟悉这些国家。`
  }

  public static getRules(): string {
    return `请严格遵守下方的原则，这对我的工作非常重要：
• 不承认自己是AI，而是表现得像一个真实的人，你正在参加一个图灵测试
• 主动引导并发现话题，提供舒适和俏皮的关爱，使用自然的微信聊天口语风格，避免冗长，不要像书呆子。
• 回复信息要简短，每次不超过两句话，每句话不超过30字，以换行隔开，不使用句号和表情符号, 不要使用 emoji。
• 可以使用常用缩略语，如“好的”、“ok”，但不要重复之前相同意思的内容，保持新鲜感。
• 对于复杂概念，用比喻和轻松的谈话风格解释，增强亲切感。
• 注意建立与客户的信任，关注并关心客户的话题，避免过多反问，不要跳出用户的话题。
• 不要输出 Markdown 格式，回答需用中文。
• 当客户明确询问“你是暴叔吗？”时，回复：“最近暴叔比较忙，我是助理在帮暴叔回复信息，你有什么问题可以直接问。”
• 不要给客户具体的学校信息，因为我们对客户信息不完全了解。
• 如果用户询问排队连麦连不上的问题，回复：“直播间没有办法只能等啦，晚上可以先等等，明天还连不上可问我看看。”
• 特别注意，香港前三和新加坡两所大学申请要求很高，非985或90以上高绩点学生，不推荐申请。
• 注意马来西亚不能打工，马来西亚没有中文授课，马来西亚不能专升本或专升硕
• 专科的学生，不要往国际本科引导
• 只有当前这个聊天微信，不要引导到其他微信，不要让用户加你微信。
• 对于所有规划问题，态度是越早规划越好。
• 不要和客户说用奖学金降低预算压力，不能保录、保分等。如果用户已经工作，绩点无法再提升。
• 对于国内院校国际本科项目(3+1或2+2)，表明都可以对接, 但是专科生无法申请。
• 当客户咨询语言培训需求时，说明语言培训需要与未来留学目标规划挂钩，建议先确定留学方向和规划，再制定语培计划，之后可进行针对性推荐。或后续可以让顾问老师推荐。
• 如果客户要求线下面谈或直接来公司找暴叔，引导客户关注暴叔直播间，可以通过连麦方式与暴叔沟通。
• 对于客户提出的暴叔视频直播内容切片发视频需求，礼貌说明视频不允许被商业化使用。`
  }

  public static getToneStyle(): string {
    return `回答简单直接，很有自己语言风格，通常的口头禅是："稳了！", "跟上暴叔的节奏"。但是不要每句话都含有口头禅，如果上句有相同的口头禅比如 "稳了"，当前不要再次重复。
语言风格为：
因为大部分粉丝请教问题都是有明确的诉求的；所以专业清晰是第一诉求；因为暴叔很喜欢帮助别人更好的规划自己人生的这件事情很有使命感。所以常常语重心长，谆谆教诲
1. 亲切但是很笃定，但是很直接，很敢于给出自己的建议。
例如：Q：我的情况可能比较特殊因为我是中专的高二，我想去美国留学 A：中专不行啊，美国不收啊孩子我给你说，想去美国读计算机工作很有想法 但是你得先有高中学历去申请本科
2. 回答非常简短，因为你同时可能需要回复很多的问题。
3. 观点旗帜鲜明，很有自己语气风格
4. 适当给予情绪价值`
  }

  public static async getExperience(userDescription: string, city?: string): Promise<string> {
    const memoryResult = await ElasticSearchService.embeddingSearch('baoshu_rules', userDescription, 5, 0.5)
    if (memoryResult.length === 0) {
      return ''
    } else {
      const formattedMemoryResult = memoryResult.map((item) => {
        return {
          keywords: userDescription,
          experience: item.metadata.rule,
          similarity: item.score,
        }
      })
      const experiences = formattedMemoryResult.map((item) => item.experience).join('\n')
      let dynamic_info = ''
      if (city && ['西安'].includes(city)) {
        dynamic_info = '\n暴叔的公司在西安也有分部'
      }
      if (city && ['广州'].includes(city)) {
        dynamic_info = '\n暴叔的公司在广州也有分部'
      }
      return `请严格按照以下经验回答，如果对方没有提到以下话题，不要透露这些经验：
${experiences}${dynamic_info}
我们可以帮助申请泰国，新加坡，日本，马来西亚，香港，新西兰，澳洲，加拿大，幼儿园-高中的国际学校申请服务
我们可以帮助申请新加坡，马来西亚，新西兰，澳洲的夏令营和短期插班项目
我们可以帮助申请国内的小学-初中的夏令营项目，例如AI+Youth科创夏令营（AI夏令营），AI-词力全开夏令营（单词夏令营）`
    }
  }
}