import { BaoshuWorkFlowNode, trackInvoke } from './baseNode'
import { IWorkflowState } from '../flow'
import { ChatStateStore, ConversationState } from '../../../storage/chat_state_store'
import { WechatMessageSender } from '../../message_send'
import { BaoshuNode } from '../type'

import chalk from 'chalk'
import { InviteToGroupHelper } from './helper/inviteToGroup'
import { LLMNode } from './llm'
import { IWecomMsgType } from '../../../../../lib/juzi/type'
import logger from '../../../../../model/logger/logger'
import { EndNode } from './end'

export class EarlyAgeInvitePendingNode extends BaoshuWorkFlowNode {
  @trackInvoke
  public static async invoke(state: IWorkflowState) {
    const chatState = ChatStateStore.get(state.chat_id)
    const userStatus = ChatStateStore.getStatus(state.chat_id)
    const userSlots = chatState.userSlots
    const nodeInvokeCount: Record<string, number> = ChatStateStore.get(state.chat_id).nodeInvokeCount

    if (userStatus.consultGroupInvited || userStatus.operationGroupInvited || nodeInvokeCount[EarlyAgeInvitePendingNode.name] > 1) {
      return EarlyAgeInvitedNode.invoke(state)
    }

    // 预算充足，拉顾问群
    if ((userSlots && userSlots.budget && userSlots.budget >= 10 && userSlots.budget_is_per_year) || (userSlots && userSlots.budget && userSlots.budget >= 70)) {
      ChatStateStore.update(state.chat_id, {
        state: ConversationState.ConsultGroupInvited,
        status: {
          consultGroupInvited: true
        }
      })

      await LLMNode.invoke({
        state,
        referenceChatHistory: true,
        dynamicPrompt: '你需要邀请用户跟专业老师沟通，参考话术: "了解了。按照您的想法，孩子的升学路径有不少方案。安排我们团队的老师和咱们进行具体的沟通。麻烦稍等，我安排。"',
        noInterrupt: true,
      })

      return BaoshuNode.EarlyAgeInvited
    } else {
      ChatStateStore.update(state.chat_id, {
        state: ConversationState.OperationGroupInvited,
        status: {
          operationGroupInvited: true
        }
      })

      await EndNode.invoke(state)

      return BaoshuNode.END

      // // 拉运营群
      // await LLMNode.invoke({
      //   state,
      //   referenceChatHistory: true,
      //   dynamicPrompt: '你需要邀请客户进入运营群, 参考话术: "孩子的规划是一个充满变数和漫长的过程，家长也需要同步学习和成长，才能跟上社会发展的节奏，我建议家长进到我们的学习规划群。群里不定期会有升学资讯，教育理念等等，您也可以随时在群里提问，会有专业的老师解答问题。"',
      //   noInterrupt: true
      // })
      //
      //
      // return BaoshuNode.EarlyAgeInvited
    }
  }
}

export class EarlyAgeInvitedNode extends BaoshuWorkFlowNode {
  @trackInvoke
  public static async invoke(state: IWorkflowState) {
    const status = ChatStateStore.getStatus(state.chat_id)

    if (status.consultGroupEntered || status.operationGroupEntered) {
      logger.debug('用户已经进群，进入通知用户直接跟顾问沟通阶段')
      return BaoshuNode.END
    }

    // 根据过去的对话，判断用户是否同意进群
    const isAgree  = await InviteToGroupHelper.isUserAgreeToEnterGroup(state) // 'true' | 'false' | 'not yet'
    logger.debug({ chat_id: state.chat_id }, chalk.redBright(`用户是否同意进群：${ isAgree }`))

    const nodeRound = ChatStateStore.getNodeEnterCount(state.chat_id, EarlyAgeInvitedNode.name)

    // 顾问群处理
    if (status.consultGroupInvited) {
      if (isAgree === 'true' ||  (nodeRound > 0 && isAgree !== 'false')) {
        await InviteToGroupHelper.inviteToCounselor(state, true)
        return BaoshuNode.END
      } else if (isAgree === 'not yet') {
        // 再问一次
        // 用户未同意进群，可以再询问下用户还有什么问题，然后再次邀请
        await LLMNode.invoke({
          state,
          dynamicPrompt: '你当前已经邀请过用户进入顾问群，用户仍处于没有直接同意或犹豫的状态，你可以先回答用户问题， 然后以你很忙，时间不够的方式，让顾问老师帮助规划的方式再次引导用户进群。或者参考这个话术："咱们的情况需要进一步的询问和讨论。我还是建议让专业的顾问老师跟你沟通下，聊聊免费的，了解清楚了才能给你最佳的方案。稍等。我拉群给你。"',
          referenceChatHistory: true,
          noInterrupt: true
        })

        await InviteToGroupHelper.inviteToCounselor(state, true)
        return BaoshuNode.END
      } else {
        await WechatMessageSender.sendById({
          user_id: state.user_id,
          chat_id: state.chat_id,
          ai_msg: `您也可以在我直播的时候进我的直播间，跟我连麦。
我直接跟您解答。`
        })

        return BaoshuNode.END
      }
    } else if (status.operationGroupInvited) {
      // // 运营群
      // if (isAgree === 'true') {
      //   logger.debug('用户同意进群，发送运营群二维码')
      //   await WechatMessageSender.sendById({
      //     user_id: state.user_id,
      //     chat_id: state.chat_id,
      //     ai_msg: '[[进群二维码]]',
      //     send_msg: {
      //       type: IWecomMsgType.Image,
      //       url: 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/551719208316_.pic_hd.jpg'
      //     }
      //   })
      //
      //   await WechatMessageSender.sendById({
      //     user_id: state.user_id,
      //     chat_id: state.chat_id,
      //     ai_msg: '可以扫码进群，群里会不定期分享资源'
      //   })
      //
      //   // 更新进群状态
      //   ChatStateStore.update(state.chat_id, {
      //     state: ConversationState.OperationGroupEntered,
      //     status: {
      //       operationGroupInvited: true,
      //       operationGroupEntered: true
      //     }
      //   })
      // } else {
      //   // 用户不同意进群，结束对话
      //   await LLMNode.invoke({
      //     state,
      //     dynamicPrompt: '当前用户不同意进群，礼貌的结束对话',
      //   })
      // }

      await EndNode.invoke(state)

      return BaoshuNode.END
    } else {
      throw new Error('未知的进群状态')
    }
  }
}