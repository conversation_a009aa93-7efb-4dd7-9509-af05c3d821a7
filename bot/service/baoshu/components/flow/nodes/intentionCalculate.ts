import { BaoshuWorkFlowNode, trackInvoke } from './baseNode'
import { IWorkflowState } from '../flow'
import { BaoshuNode } from '../type'
import { ChatHistoryService } from '../../chat_history'
import { LLM } from '../../../../../lib/ai/llm/LLM'
import { IntentionCategorizePrompt } from '../../../prompt/baoshu/intention_categorize'
import { XMLHelper } from '../../../../../lib/xml/xml'
import { RegexHelper } from '../../../../../lib/regex/regex'
import { ChatStateStore, ConversationState } from '../../../storage/chat_state_store'
import { ObjectUtil } from '../../../../../lib/object'
import { StudyPlanRoutingNode } from './studyAbroadPlan'
import { LLMNode } from './llm'
import { IsAICompleteSlotAskTaskPrompt } from '../../../prompt/baoshu/is_slot_ask'
import { EducationGroupCategoryPrompt } from '../../../prompt/baoshu/education_group_category'
import { EarlyAgeInvitePendingNode } from './earlyAge'
import { SlotPromptHelper } from './helper/slotPrompt'
import chalk from 'chalk'
import { UserSlotsExtract } from './helper/slotsExtract'
import { WechatMessageSender } from '../../message_send'
import { HumanTransfer, HumanTransferType } from '../../human_transfer'
import { DoctorConsultNode } from './doctorConsult'
import { InviteToConsultantNode } from './inviteToGroup'
import { PostSaleInviteNode } from './postSale'
import { UserInfoExtractPrompt } from '../../../prompt/baoshu/user_info_extract'
import { BooleanUserInfoExtractPrompt } from '../../../prompt/baoshu/study_abroad_and_is_parent'
import { IsParentSupportedPrompt } from '../../../prompt/baoshu/is_parent_support'
import { LLMXMLHelper } from './helper/xmlHelper'
import { IWecomMsgType } from '../../../../../lib/juzi/type'
import { OperationGroupRecommendationPrompt } from '../../../prompt/baoshu/group_pick'
import { BudgetReConfirmNode } from './budgetReconfirm'
import { BudgetFromCounselorPrompt } from '../../../prompt/baoshu/budget_form_counselor'
import logger from '../../../../../model/logger/logger'
import { UserQuerySupplement } from './helper/userQuerySupplement'
import { ExtractEducationLevelPrompt } from '../../../prompt/baoshu/extract_education_level'
import { sleep } from '../../../../../lib/schedule/schedule'
import { ParentReconfirmNode } from './parentReconfirm'
import { FillFormNode } from './fillForm'
import { EndNode } from './end'

export enum UserIntentionType {
  AcademicPlanning = 1, // 正常路线，留学咨询
  SeekingPartnership = 2, // 合作
  PostSaleComplaint = 3, // 投诉
  OfferComparison = 4, // Offer 比较
  GuaranteedAdmissionInquiry = 5, // 保录
  SchoolTransferInquiry = 6, // 转校
  JoinOperationalGroup = 7, // 加运营群
  NonStudyAbroad = 8, // 非留学
  NonChineseNationalityInquiry = 9, // 非中国国籍
  DirectFindCounselor = 10
}

export enum UserEducationGroup { // 注意这里的顺序不要轻易更改，会影响搜索时候的索引 getPreQuery educationGroups = ['初中', '高中', '本科', '大专']
  BelowJuniorOne = 0, // 初一及以下
  JuniorTwoToFour = 1, // 初二到初四
  HighSchool = 2, // 高中
  University = 3, // 本科
  College = 4,  // 大专
  Master = 5, // 研究生
  Doctor = 6, // 博士
  JapanHighSchool = 7, // 高中生，学日语
  Gaokao = 8, // 高考生
}

export enum UserSlotType {
  NoneInfo = 'none_info',

  // 公共槽位
  CurrentEducation = 'current_level_of_education',
  Goal = 'goal',
  PreferredPlan = 'preferred_plan', // 方案偏好
  Budget = 'budget',
  IsBudgetPerYear = 'budget_is_per_year',
  City = 'city',
  IsStudyAbroad = 'is_study_abroad',
  IsParent = 'is_parent',

  // 不同留学阶段的附加槽位
  IsParentSupported = 'is_parent_supported',
  GPA = 'gpa',
  LanguageTestScore = 'language_test_score',
  School = 'school',
  Major = 'major',
  IntendedProject = 'user_intended_country',
  ApplicationStatus = 'application_status',
  IsGaokaoStudent = 'is_gaokao_student',
}

export enum FinishReason {
  ParentNotSupport = 0,
  BudgetNotEnough= 1
}

export const defaultSlotAsk = '回答用户问题'


export class IntentionCalculateNode extends BaoshuWorkFlowNode {
  @trackInvoke
  public static async invoke(state: IWorkflowState) {
    const nodeInvokeCount = ChatStateStore.getNodeEnterCount(state.chat_id, IntentionCalculateNode.name)

    if (nodeInvokeCount >= 50) { // 防止无限聊下去
      return await EndNode.invoke(state)
    }

    let intentionCategory
    let querySlot

    if (nodeInvokeCount <= 4) {
      // 并发意图和槽位提取提升性能
      const [intentionCategoryResult, querySlotResult] = await Promise.all([
        this.intentionCategorize(state.chat_id, state.userMessage),
        this.getQuerySlot(state.chat_id, state.user_id, state.round_id)
      ])
      intentionCategory = intentionCategoryResult
      querySlot = querySlotResult
      const intentions = ChatStateStore.get(state.chat_id).intentions
      intentions.push(String(intentionCategory))
      ChatStateStore.update(state.chat_id, {
        intentions: intentions
      })

      if (intentions.length >= 3 && intentions.every((intention) => intention === 'null' && querySlot && querySlot.slotType !== UserSlotType.NoneInfo)) {
        logger.warn('用户连续三次没有表达留学意向')
        return BaoshuNode.END
      }

      logger.log(`intentionCategory: ${ObjectUtil.enumKeys(UserIntentionType)[intentionCategory - 1]}`)


      if (intentionCategory === UserIntentionType.DirectFindCounselor) {
        await HumanTransfer.transfer(state.chat_id, state.user_id, HumanTransferType.FindCounselor)
      } else if (intentionCategory === UserIntentionType.SeekingPartnership) {
        await WechatMessageSender.sendById({
          user_id: state.user_id,
          chat_id: state.chat_id,
          ai_msg: '稍等，我让具体老师来对接下'
        })
        logger.debug({ chat_id: state.chat_id }, '合作拉群')

        try {
          await PostSaleInviteNode.invoke(state, 'cooperation')
        } catch (e) {
          await HumanTransfer.transfer(state.chat_id, state.user_id, HumanTransferType.FailedToJoinGroup)
        }
        return BaoshuNode.END
      } else if (intentionCategory === UserIntentionType.GuaranteedAdmissionInquiry) {
        await WechatMessageSender.sendById({
          user_id: state.user_id,
          chat_id: state.chat_id,
          ai_msg: '这个不能整！风险大，而且万一后续查出来造假后果很严重。咱能走正规途径还是走正规途径。'
        })

        logger.debug({ chat_id: state.chat_id }, '保录')
        return await InviteToConsultantNode.invoke(state)
      } else if (intentionCategory === UserIntentionType.SchoolTransferInquiry) {
        await HumanTransfer.transfer(state.chat_id, state.user_id, HumanTransferType.SchoolTransferInquiry)
        return BaoshuNode.END
      } else if (intentionCategory === UserIntentionType.JoinOperationalGroup) {
        logger.debug({ chat_id: state.chat_id }, '拉运营群')
        await this.inviteToOperationGroup(state)
        return BaoshuNode.END
      } else if (intentionCategory === UserIntentionType.NonStudyAbroad || intentionCategory === UserIntentionType.NonChineseNationalityInquiry) {
        await HumanTransfer.transfer(state.chat_id, state.user_id, HumanTransferType.NonStudyAbroad)
        return BaoshuNode.END
      }

    } else {
      querySlot =  await this.getQuerySlot(state.chat_id, state.user_id, state.round_id)
    }

    // 获取对应的槽位，及顺序
    const chatState = ChatStateStore.get(state.chat_id)
    const userSlots = chatState.userSlots

    // 如果用户明确体制内
    if (userSlots && (userSlots.is_study_abroad === 'domestic' || userSlots.is_study_abroad === 'false')) {
      logger.debug({ chat_id: state.chat_id }, '用户明确表示走体制内')
      await LLMNode.invoke({
        state,
        dynamicPrompt: '客户明确表示不出国，可以回答一下用户问题，以对体制内不够了解结束对话',
      })

      return BaoshuNode.END
    }


    // 硕，博 询问预算后 拉群
    const slotAskedCount = ChatStateStore.get(state.chat_id).slotAskedCount
    const hasBudgetOrAskedBudget =  (userSlots && userSlots?.budget) || slotAskedCount[UserSlotType.Budget] >= 2

    if (userSlots && hasBudgetOrAskedBudget && (userSlots.education_group === UserEducationGroup.Master || userSlots.education_group === UserEducationGroup.Doctor) && (userSlots.application_stage !== '硕士')) {
      return await DoctorConsultNode.invoke(state)
    }

    // 本科身份，并且已经有offer; 或者已经有中介 拉运营群
    if (userSlots && ((userSlots.has_offer && userSlots.education_group === UserEducationGroup.University) || userSlots.has_agent === true)) {
      logger.debug({ chat_id: state.chat_id }, '用户已有offer或者中介, 拉运营群，然后结束')
      await this.inviteOperationGroup(state)
      return BaoshuNode.END
    }

    // 如果是 高中以下，非家长，呼唤家长来沟通，直到家长过来后， is_parent 改为 true 才会继续往后面走
    if (userSlots && userSlots.education_group && userSlots.education_group < UserEducationGroup.HighSchool && userSlots.is_parent === false) {
      if (chatState.state !== ConversationState.ConfirmedIsParent) {
        ChatStateStore.update(state.chat_id, { state: ConversationState.ConfirmedIsParent })

        await LLMNode.invoke({
          state,
          referenceChatHistory: true,
          dynamicPrompt: `客户当前年级在高一以下，因为孩子没有决策内容，结合上文孩子遇到的问题，如果孩子询问的是单点问题，回答后自然引导家长来沟通。如果是规划性问题，简单安慰回复当前其面对的问题后，引导其让家长沟通。
例如：
暴叔：孩子，才初一，都来得及，别着急。另外你现在年纪很小，很多事决定需要大人来做主。
我的建议先跟父母商量和讨论你的想法，让你的父母来找我沟通，我们一起帮你。不过不论咋样，都要努力学习，未来才有更多选择的机会。
加油！"`,
          promptName: 'InviteParentsInvolve'
        })
      }

      logger.log({ chat_id: state.chat_id }, '高一以下非家长，等待家长介入，不进行回复')

      // 仍然会在下一轮，确认是否为父母
      return BaoshuNode.IntentionCalculate
    }

    if (querySlot && querySlot.isFinish) {
      await LLMNode.invoke({ state, dynamicPrompt: querySlot.slotQuestion })
      if (querySlot.finishReason === FinishReason.ParentNotSupport) {
        return ParentReconfirmNode.invoke(state)
      } else {
        return BaoshuNode.END
      }
    } else if (querySlot && querySlot.slotQuestion) { // 主路线
      await this.slotAsk(querySlot.slotQuestion, state, querySlot.slotType)
      return BaoshuNode.IntentionCalculate
    } else {
      // 槽位已满
      const userSlots = ChatStateStore.get(state.chat_id).userSlots
      // 最后把 education_group 更新一下
      if (userSlots) {
        userSlots.education_group = await this.getUserEducationGroup(userSlots.current_level_of_education, userSlots.grade, userSlots.application_stage, userSlots.is_japanese_student, userSlots.is_gaokao_student)
      }

      logger.log({ chat_id: state.chat_id }, chalk.redBright('最终的用户槽位', JSON.stringify(userSlots, null, 2)))

      // 如果还是无预算的话，需要再确认一轮
      if (userSlots && !userSlots.budget) {
        return await BudgetReConfirmNode.invoke(state)
      }

      if (userSlots && userSlots.education_group === UserEducationGroup.BelowJuniorOne) {
        return await EarlyAgeInvitePendingNode.invoke(state)
      }

      return await StudyPlanRoutingNode.invoke(state)
    }
  }

  public static async intentionCategorize(chat_id: string, userMessage: string, prompt?: string, modelType?: string) {
    const userMessageCount = await ChatHistoryService.getUserMessageCount(chat_id)
    if (userMessageCount <= 3 && this.includeCounselorFamilyName(userMessage)) {
      return UserIntentionType.DirectFindCounselor
    }

    if (userMessage.trim().length < 4) {
      return UserIntentionType.AcademicPlanning
    }

    try {
      const llm = new LLM({ model: modelType, meta: { promptName: 'IntentionCategorize', description: '意图分类', chat_id } })
      const llmRes = await llm.predict(prompt ? prompt : await IntentionCategorizePrompt.format(userMessage))

      logger.debug({ chat_id }, '意图分类结果: ', llmRes)
      const intention =  XMLHelper.extractContent(llmRes, 'answer')

      if (!intention) {
        return UserIntentionType.AcademicPlanning
      }

      const intentionType =  RegexHelper.extractNumber(intention) as UserIntentionType

      if (!ObjectUtil.enumValues(UserIntentionType).includes(intentionType)) {
        logger.warn({ chat_id }, '意图分类失败', intentionType)
        return UserIntentionType.AcademicPlanning
      }

      // 特殊 case, 通过其他模型，再判断一次， 比如 gpt4-turbo, claude 3.5, 如果一致，则确定。不一致，再判断一次。
      if ([UserIntentionType.OfferComparison, UserIntentionType.GuaranteedAdmissionInquiry, UserIntentionType.SchoolTransferInquiry, UserIntentionType.NonStudyAbroad, UserIntentionType.DirectFindCounselor].includes(intentionType) && !modelType) { // 注意这里递归调用， 要检查 modelType 防止死循环
        const secondaryModelType = 'claude-3-5-sonnet-20240620'
        const secondaryIntentionType = await this.intentionCategorize(chat_id, userMessage, prompt, secondaryModelType)

        if (intentionType === secondaryIntentionType) {
          return intentionType
        } else {
          return UserIntentionType.AcademicPlanning
        }
      }
      return intentionType
    } catch (e) {
      logger.error({ chat_id }, '意图分类失败', e)
      return UserIntentionType.AcademicPlanning
    }
  }

  /**
   * 获取要提问的一个槽位，如果没有则返回 null
   * @param chat_id
   * @param user_id
   * @param round_id
   * @private
   */
  public static async getQuerySlot(chat_id: string, user_id: string, round_id: string) {
    // 获取要提问的槽位。取要要提问的槽位中，去除已有槽位的第一个
    const chatHistory = await ChatHistoryService.formatHistory(chat_id)
    const chatState = ChatStateStore.get(chat_id)
    const prevUserSlots = chatState.userSlots ?? {}

    const prompt = await UserInfoExtractPrompt.format(chatHistory)
    const booleanPrompt = await BooleanUserInfoExtractPrompt.format(chatHistory)
    const currentUserSlots = await UserSlotsExtract.extract(prompt, booleanPrompt, { chat_id, user_id, round_id })

    if (ObjectUtil.isEmptyObject(currentUserSlots)) {
      return {
        slotQuestion: '用户目前没有告知任何信息，需要进行询问。例如："你好，请说"',
        slotType: UserSlotType.NoneInfo,
      }
    }

    let userSlots = prevUserSlots

    if (chatState.state === ConversationState.AskedParentSupport && userSlots.is_parent_supported === undefined) {
      userSlots.is_parent_supported = await UserSlotsExtract.isParentSupported(await IsParentSupportedPrompt.format(chatHistory))
    }

    if (currentUserSlots) {
      userSlots = await this.mergeUserSlots(userSlots, currentUserSlots, chat_id)
      // 根据用户当前所在的年级获取对应的槽位
      userSlots.education_group = await this.getUserEducationGroup(userSlots.current_level_of_education, userSlots.grade, userSlots.application_stage, userSlots.is_japanese_student, userSlots.is_gaokao_student)

      ChatStateStore.update(chat_id, { userSlots: userSlots })
    }

    logger.debug({ chat_id }, '用户槽位:', JSON.stringify(userSlots, null, 4))

    // 家里不支持 pass
    if (userSlots.is_parent_supported === false) {
      return {
        slotQuestion: '家里不支持留学，建议先跟家里商量好，再来找叔',
        slotType: UserSlotType.IsParentSupported,
        isFinish: true,
        finishReason: FinishReason.ParentNotSupport
      }
    }

    // 年预算 < 10w 直接 pass， 除博士以外
    if ((userSlots && userSlots.education_group !== UserEducationGroup.Doctor) && ((userSlots.budget_is_per_year && userSlots.budget && userSlots.budget < 10) || (userSlots.budget && userSlots.budget < 10))) {
      return {
        slotQuestion: '用户当前预算太低，可以用类似的语句委婉回复：“咱这个预算可能不太够，可以先存存钱，之后再来找叔，出国至少一年要准备 10w 以上的”。',
        slotType: UserSlotType.Budget,
        isFinish: true,
        finishReason: FinishReason.BudgetNotEnough
      }
    }

    // 首先必须去问所在年级
    if (!userSlots.is_working && !userSlots.current_level_of_education && !userSlots.education_group) {
      return {
        slotQuestion: '询问用户当前所在的教育背景的年级，例如："咱们现在几年级？"',
        slotType: UserSlotType.CurrentEducation,
      }
    }

    const userEducationGroupSlotsMap = new Map<UserEducationGroup, UserSlotType[]>([
      [UserEducationGroup.BelowJuniorOne, [UserSlotType.City, UserSlotType.Budget, UserSlotType.IsBudgetPerYear, UserSlotType.IsStudyAbroad]],
      [UserEducationGroup.JuniorTwoToFour, [UserSlotType.IsParent, UserSlotType.Goal, UserSlotType.PreferredPlan, UserSlotType.IsStudyAbroad, UserSlotType.GPA, UserSlotType.City, UserSlotType.Budget, UserSlotType.IsBudgetPerYear]],
      [UserEducationGroup.HighSchool, [UserSlotType.IsParent, UserSlotType.Goal, UserSlotType.PreferredPlan, UserSlotType.IsStudyAbroad, UserSlotType.IsParentSupported, UserSlotType.GPA,  UserSlotType.Budget, UserSlotType.IsBudgetPerYear, UserSlotType.IntendedProject, UserSlotType.LanguageTestScore, UserSlotType.City]],
      [UserEducationGroup.University, [UserSlotType.Goal, UserSlotType.PreferredPlan, UserSlotType.IsStudyAbroad, UserSlotType.School, UserSlotType.GPA, UserSlotType.Budget, UserSlotType.IsBudgetPerYear, UserSlotType.Major, UserSlotType.City]],
      [UserEducationGroup.College, [UserSlotType.Goal, UserSlotType.PreferredPlan, UserSlotType.IsStudyAbroad, UserSlotType.Budget, UserSlotType.IsParent, UserSlotType.LanguageTestScore, UserSlotType.Major, UserSlotType.City]],
      [UserEducationGroup.Master, [UserSlotType.Goal, UserSlotType.IsStudyAbroad, UserSlotType.Budget, UserSlotType.GPA, UserSlotType.Major]],
      [UserEducationGroup.Doctor, [UserSlotType.Budget]],
      [UserEducationGroup.JapanHighSchool, [UserSlotType.IsParent, UserSlotType.PreferredPlan, UserSlotType.IsStudyAbroad, UserSlotType.IntendedProject, UserSlotType.City, UserSlotType.GPA, UserSlotType.LanguageTestScore, UserSlotType.Budget, UserSlotType.IsBudgetPerYear]],
      [UserEducationGroup.Gaokao, [UserSlotType.IsParent, UserSlotType.Goal, UserSlotType.PreferredPlan, UserSlotType.IsStudyAbroad, UserSlotType.IntendedProject, UserSlotType.City, UserSlotType.GPA, UserSlotType.LanguageTestScore, UserSlotType.Budget, UserSlotType.IsBudgetPerYear]],
    ])

    const slots = userEducationGroupSlotsMap.get(userSlots.education_group as UserEducationGroup)

    if (!slots) { // 这里兜下底
      logger.error({ chat_id }, '未知的用户年级', userSlots.education_group)
      return {
        slotQuestion: defaultSlotAsk,
        slotType: UserSlotType.Goal,
      }
    }

    ObjectUtil.keys(userSlots).forEach((key) => {
      if (slots.includes(key as UserSlotType) && !([UserSlotType.IsBudgetPerYear, UserSlotType.IsParentSupported].includes(key as UserSlotType))) {
        slots.splice(slots.indexOf(key as UserSlotType), 1)
      }
    })

    if (slots.length >= 1) {
      const slotAskedCount = ChatStateStore.get(chat_id).slotAskedCount

      for(let i = slots.length - 1; i >= 0; i--) { // 倒序删除，防止 index 错误变化
        const slotType = slots[i] as UserSlotType

        if (slotType === UserSlotType.PreferredPlan) {
          if ((userSlots.user_intended_country && userSlots.user_intended_country.length > 0) || (userSlots.user_intended_project && userSlots.user_intended_project.length > 0) || (userSlots.user_intended_school && userSlots.user_intended_school.length > 0)) {
            if (userSlots.education_group === UserEducationGroup.College || userSlots.education_group === UserEducationGroup.University) {
              slots.splice(i, 1)
              continue
            }
          }
        }


        if (slotType === UserSlotType.IntendedProject) {
          if ((userSlots.user_intended_country && userSlots.user_intended_country.length > 0) || (userSlots.user_intended_project && userSlots.user_intended_project.length > 0) || (userSlots.user_intended_school && userSlots.user_intended_school.length > 0)) {
            slots.splice(i, 1)
            continue
          }

          // 用户如果表示不出国，不再询问
          if ((userSlots.is_study_abroad === 'false' || userSlots.is_study_abroad === 'domestic' || userSlots.is_study_abroad === false)) {
            slots.splice(i, 1)
            continue
          }
        }

        // 所有槽位最多询问两次
        if (slotAskedCount[slotType] && slotAskedCount[slotType] > 2) {
          slots.splice(i, 1)
          continue
        }

        if (slotAskedCount[slotType] && slotAskedCount[slotType] >= 1) { // 只问一次的槽位
          if ([UserSlotType.PreferredPlan, UserSlotType.Goal, UserSlotType.IsBudgetPerYear, UserSlotType.IsParentSupported, UserSlotType.GPA, UserSlotType.City].includes(slotType)) {
            slots.splice(i, 1)
            continue
          }
        }

        // 大一不问 GPA
        if (userSlots.grade === '大一' && slotType === UserSlotType.GPA) {
          slots.splice(i, 1)
          continue
        }


        // 预算 >= 40w 或 无预算, 也不问是否为每年预算
        if (slotType === UserSlotType.IsBudgetPerYear) {
          if ((userSlots.budget && userSlots.budget >= 40) || (userSlots.budget_is_per_year === true) || (userSlots.budget === 9999)) {
            slots.splice(i, 1)
            continue
          }

          // 如果已经问过了预算，budget 字段还是 未知，不要问每年的预算
          if (slotAskedCount[UserSlotType.Budget] && slotAskedCount[UserSlotType.Budget] >= 1 && !userSlots.budget) {
            slots.splice(i, 1)
            continue
          }
        }

        if (slotType === UserSlotType.IsParentSupported) {
          if (userSlots.is_parent || userSlots.is_parent_supported === true || userSlots.is_parent_supported === false) {
            slots.splice(i, 1)
          }
        }
      }
    }

    if (slots.length === 0) {
      return null
    }

    if (userSlots.application_status && userSlots.application_status === '已完成') {
      return {
        slotQuestion: '用户已经完成了申请，可以礼貌的结束对话',
        slotType: UserSlotType.ApplicationStatus,
        isFinish: true
      }
    }

    let slotQuestion = SlotPromptHelper.getPrompt(slots[0] as UserSlotType, userSlots.education_group as UserEducationGroup)
    if (!slotQuestion) {
      if (userSlots.education_group) {
        logger.error({ chat_id }, '暂时无法处理的类型', slots[0], ObjectUtil.enumKeys(UserEducationGroup)[userSlots.education_group])
      }

      return null
    }

    const slotAskedCount = ChatStateStore.get(chat_id).slotAskedCount
    if (slots[0] as UserSlotType === UserSlotType.Budget && slotAskedCount[UserSlotType.Budget] && slotAskedCount[UserSlotType.Budget] >= 1) { // 预算回答不知道，第二次提问可以附带一些预算信息
      let budgetEstimate: string
      switch (userSlots.education_group) {
        case UserEducationGroup.BelowJuniorOne:
          budgetEstimate = '越早送孩子出去花费其实就越大的，但是总体来说培养到本科毕业的话，总共准备个150-250万是要的。具体看咱们走什么个路线了。'
          break
        case UserEducationGroup.JuniorTwoToFour:
          budgetEstimate = '在国内读国际高中的话，便宜的学费十来万一年，基本十几万到三十几万一年，具体也看地区和学校。出国的话，就看到哪里读了，英国澳加一年30-50w是跑不了。'
          break
        case UserEducationGroup.HighSchool:
          budgetEstimate = '想出国的话，性价比最高比如马来西亚，韩国之类，最少也要10w/年，英美澳加 40-50w/年，新加坡香港之类的要25-35w/年了。如果考虑国际本科的话，费用可以的少点，平均15-20w/年就可以。'
          break
        case UserEducationGroup.University:
          budgetEstimate = '海外硕士学制都相对短，1-2年居多。少的韩国马来西亚一年10w,新加坡香港30-40w/年，相对来说英美澳加贵点，每年40w以上了'
          break
        default:
          budgetEstimate = '想出国的话，性价比最高比如马来西亚，韩国之类，最少也要10w/年，英美澳加 40-50w/年，新加坡香港之类的要25-35w/年了。如果考虑国际本科的话，费用可以的少点，平均15-20w/年就可以。'
          break
      }

      slotQuestion = `用户对预算没有概念，需要提供一个大概的范围作为参考。例如："${budgetEstimate}"`

      ChatStateStore.update(chat_id, {
        state: ConversationState.BudgetSupplemented,
        status: {
          budgetSupplemented: true
        }
      })

      return {
        slotQuestion,
        slotType: slots[0] as UserSlotType,
      }
    }

    if (userSlots.education_group) {
      logger.debug({ chat_id }, '要提问的槽位', ObjectUtil.enumKeys(UserEducationGroup)[userSlots.education_group], slots[0], slotQuestion)
    }

    // 家里是否支持
    if (slots[0] as UserSlotType === UserSlotType.IsParentSupported) {
      ChatStateStore.update(chat_id, {
        state: ConversationState.AskedParentSupport
      })
    }

    // 获取槽位问题
    return {
      slotQuestion,
      slotType: slots[0] as UserSlotType
    }
  }

  private static async slotAsk(querySlotPrompt: null | string, state: IWorkflowState, slotType: UserSlotType) {
    if (!querySlotPrompt) {
      logger.warn('querySlot is empty')
      return
    }

    // 如果已经有当前的阶段，则拼接上推荐优先级项目
    const userSlots = ChatStateStore.get(state.chat_id).userSlots
    let supplementPrompt = ''
    if (userSlots && !userSlots.budget) {
      supplementPrompt = '**一定要注意：现在用户没有告知预算，一定不要给用户主动推荐国家或项目，因为项目推荐会不准确, 只进行提问即可**'
    } else if (userSlots && userSlots.education_group) {
      if (ChatStateStore.get(state.chat_id).pickedMiddleProject) {
        // 移除预算能提高么
        let pickedMiddleProject = ChatStateStore.get(state.chat_id).pickedMiddleProject as string
        if (pickedMiddleProject.includes('例如：你这个预算可能不太够啊，去 xx 大概需要 xx, 你预算能提高么')) {
          pickedMiddleProject = pickedMiddleProject.replace('例如：你这个预算可能不太够啊，去 xx 大概需要 xx, 你预算能提高么', '').replace('用户的预算对于想要去的国家或学校不够，礼貌的询问，预算可以提高吗。并告知合理预算范围多少。\n', '')
        }

        supplementPrompt = pickedMiddleProject
      } else {
        const chatHistory = await ChatHistoryService.formatHistory(state.chat_id)
        supplementPrompt = await UserQuerySupplement.getSupplementPrompt(state.chat_id, userSlots, userSlots.education_group, chatHistory)
      }
    }

    // 槽位提问计数 + 1
    const slotCount = ChatStateStore.get(state.chat_id).slotAskedCount
    slotCount[slotType] = slotCount[slotType] ? slotCount[slotType] + 1 : 1
    ChatStateStore.update(state.chat_id, { slotAskedCount: slotCount })

    // 在前面计数，如果上次槽位提问还没结束，当前又要对当前槽位进行提问，有重复的可能性，舍掉当前的提问
    const aiResponse =  await LLMNode.invoke({
      state,
      dynamicPrompt: `${querySlotPrompt}\n\n${supplementPrompt}`,
      checkRepeat: true,
    })

    if (slotType === UserSlotType.NoneInfo) {
      return
    }

    if (slotType === UserSlotType.Budget && ChatStateStore.get(state.chat_id).state === ConversationState.BudgetSupplemented) { // 预算补充信息不进行二次输出
      if (aiResponse.includes('w') || aiResponse.includes('万')) { // 预算范围的去重特殊一点
        return
      }
    }

    // 校验 AI 是否完成了任务，如果没有的话，通过规则匹配问出槽位问题
    const llm = new LLM()
    let isAiCompleteSlotAskTask = /.*[？?么吗啥].*/.test(aiResponse) ? 'true' : 'false' // 问句检查

    if (isAiCompleteSlotAskTask !== 'true') {
      const llmRes  = await llm.predict(await IsAICompleteSlotAskTaskPrompt.format(querySlotPrompt, aiResponse))
      const llmAnswer = XMLHelper.extractContent(llmRes, 'answer')

      if (!llmAnswer) {
        logger.error('isAiCompleteSlotAskTask xmlRes error', llmAnswer)
        return
      }

      if (llmAnswer !== 'false') {
        if (llmAnswer !== 'true')
          logger.error('isAiCompleteSlotAskTask xmlRes error', llmAnswer)
        return
      }

      isAiCompleteSlotAskTask = llmAnswer
    }

    if (isAiCompleteSlotAskTask === 'true') {
      return
    }

    if (querySlotPrompt === defaultSlotAsk) {
      return
    }

    if (ChatStateStore.getStatus(state.chat_id).budgetSupplemented) {
      return
    }

    await sleep(5000)

    const supplementAsk =  await LLMNode.invoke({
      state,
      referenceChatHistory: true,
      dynamicPrompt: querySlotPrompt,
      checkRepeat: true,
      noInterrupt: true,
      regenerate: true
    })

    logger.debug({ chat_id: state.chat_id }, '补充提问', supplementAsk)
  }


  private static async getUserEducationGroup(currentLevelOfEducation: string | undefined, grade: string| undefined, application_stage: string | undefined, is_japanese_student: boolean| undefined, is_gaokao: boolean|undefined) {
    if (grade === '高三' || is_gaokao) {
      return UserEducationGroup.HighSchool
    }

    if (application_stage === '博士') {
      return UserEducationGroup.Doctor
    }

    // 初中以外，以 currentLevelOfEducation 作为优先判断
    if (currentLevelOfEducation && ['低龄', '小学',].includes(currentLevelOfEducation)) {
      return UserEducationGroup.BelowJuniorOne
    }

    if (currentLevelOfEducation && ['职高', '中专', '技校', '高中'].includes(currentLevelOfEducation)) {
      return UserEducationGroup.HighSchool
    }

    if (currentLevelOfEducation === '本科') {
      return UserEducationGroup.University
    }

    if (currentLevelOfEducation === '大专' || currentLevelOfEducation === '专科') {
      return UserEducationGroup.College
    }

    if (currentLevelOfEducation === '研究生' || currentLevelOfEducation === '硕士') {
      return UserEducationGroup.Master
    }

    if (currentLevelOfEducation === '博士') {
      return UserEducationGroup.Doctor
    }

    if (grade && ['一年级', '二年级', '三年级', '四年级', '五年级', '六年级', '初一'].includes(grade)) {
      return UserEducationGroup.BelowJuniorOne
    }

    if (grade && ['初二', '初三', '初四'].includes(grade)) {
      return UserEducationGroup.JuniorTwoToFour
    }

    if (currentLevelOfEducation && currentLevelOfEducation.includes('初中')) {
      return UserEducationGroup.JuniorTwoToFour
    }

    if (grade && ['高一', '高二', '高三'].includes(grade)) {
      return UserEducationGroup.HighSchool
    }

    if (grade && ['大一', '大二', '大三', '大四'].includes(grade)) {
      return UserEducationGroup.University
    }

    if (grade && ['研一', '研二', '研三'].includes(grade)) {
      return UserEducationGroup.Master
    }

    if (grade && ['博一', '博二', '博三', '博四', '博五', '博六'].includes(grade)) {
      return UserEducationGroup.Doctor
    }

    // 其他情况
    if (currentLevelOfEducation && grade) {
      // 调用模型判断
      const llm = new LLM()
      const xmlRes = await llm.predict(await EducationGroupCategoryPrompt.format(currentLevelOfEducation, grade))
      const educationGroup =  RegexHelper.extractNumber(XMLHelper.extractContent(xmlRes, 'answer'))

      if (!educationGroup || educationGroup < 1 || educationGroup > 9) {
        return UserEducationGroup.HighSchool
      } else {
        return educationGroup - 1 as UserEducationGroup
      }
    }
  }

  private static async inviteToOperationGroup(state: IWorkflowState) {
    // 根据用户的意图 分流到不同的群中
    let groupName: string
    const groupQrCodeMap = {
      '单词群': 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/baoshu/qrcode/word_memory.jpg',
      '低龄规划群': 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/551719208316_.pic_hd.jpg',
      '本科规划群': 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/baoshu/qrcode/university.jpg',
      '硕士规划群': 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/baoshu/qrcode/master.jpg',
    }
    if (state.userMessage.includes('单词群') || state.userMessage.includes('单词')) {
      groupName = '单词群'
    } else if (state.userMessage.includes('低龄规划群') || state.userMessage.includes('低龄')) {
      groupName = '低龄规划群'
    } else if (state.userMessage.includes('本科规划群') || state.userMessage.includes('本科')) {
      groupName = '本科规划群'
    } else if (state.userMessage.includes('硕士规划群') || state.userMessage.includes('硕士') || state.userMessage.includes('研究生'))  {
      groupName = '硕士规划群'
    } else {
      // 模型根据历史记录判断进哪个群
      const chatHistory = await ChatHistoryService.formatHistory(state.chat_id)
      const group =  await this.chooseGroup(await OperationGroupRecommendationPrompt.format(chatHistory))
      if (group === null || !['单词群', '低龄规划群', '本科规划群', '硕士规划群'].includes(group)) {
        await HumanTransfer.transfer(state.chat_id, state.user_id, HumanTransferType.QueryGroup)
      }
      groupName = group as string
    }

    const qrCodeLink = groupQrCodeMap[groupName]

    await WechatMessageSender.sendById({
      user_id: state.user_id,
      chat_id: state.chat_id,
      ai_msg: '[[进群二维码]]',
      send_msg: {
        type: IWecomMsgType.Image,
        url: qrCodeLink
      }
    })

    ChatStateStore.update(state.chat_id, {
      state: ConversationState.OperationGroupInvited,
      status: {
        operationGroupInvited: true,
        operationGroupEntered: true
      }
    })
  }

  private static async chooseGroup(prompt: string) {
    const llm = new LLM()
    const llmRes = await llm.predict(prompt)
    if (llmRes) {
      return XMLHelper.extractContent(llmRes, 'answer')
    }

    return null
  }

  /**
   * 槽位更新，注意有些特殊槽位更新前要做校验
   * @param userSlots
   * @param currentUserSlots
   * @param chat_id
   * @private
   */
  private static async mergeUserSlots(userSlots: Record<string, any>, currentUserSlots: Record<string, any>, chat_id: string) {
    // 注意如果 新预算是从 顾问侧提取的，不要去更新
    if (currentUserSlots.budget !== userSlots.budget) {
      const chatHistory = await ChatHistoryService.formatHistoryOnRole(chat_id, 'assistant')
      if (chatHistory.includes(currentUserSlots.budget)) { // 简单规则，如果顾问侧历史记录中包含当前预算，那么就是从顾问侧提取的，不要更新
        currentUserSlots.budget = userSlots.budget
        currentUserSlots.budget_is_per_year = userSlots.budget_is_per_year
      } else{
        // 使用模型判断下 新预算是否是从 顾问侧提取的
        const prompt = await BudgetFromCounselorPrompt.format(String(`${currentUserSlots.budget}万`), chatHistory)
        const res = await LLMXMLHelper.extractBooleanAnswer(prompt, { tagName: 'Result', trueFlag: 'YES', falseFlag: 'NO', model: 'gpt-4o-mini' })

        if (res) {
          currentUserSlots.budget = userSlots.budget
          currentUserSlots.budget_is_per_year = userSlots.budget_is_per_year
        }
      }
    }

    // 合并 意向国家
    if (Array.isArray(currentUserSlots.user_intended_country) && Array.isArray(userSlots.user_intended_country)) {
      currentUserSlots.user_intended_country = [...new Set([...userSlots.user_intended_country, ...currentUserSlots.user_intended_country])]
    }

    if (ChatStateStore.getStatus(chat_id).is_update_is_parent_support) {
      currentUserSlots.is_parent_supported = true
    }

    // current_level_of_education 判断优化
    if (currentUserSlots.current_level_of_education && currentUserSlots.current_level_of_education === '硕士') {
      if (userSlots.current_level_of_education !== '硕士') {
        // 硕士会直接拉入到博士咨询，这里需要进行二次校验
        const current_level_of_education = await this.extractUserEducationGroup(chat_id)
        if (current_level_of_education) {
          currentUserSlots.current_level_of_education = current_level_of_education
        }
      }
    }

    return  ObjectUtil.merge(userSlots, currentUserSlots)
  }

  private static async extractUserEducationGroup(chat_id: string) {
    // 提取用户的教育背景
    const chatHistory = await ChatHistoryService.formatHistory(chat_id)
    const prompt = await ExtractEducationLevelPrompt.format(chatHistory)
    const llmRes = await LLM.predict(prompt)
    const xmlRes = XMLHelper.extractContent(llmRes, 'extracted_info')

    if (!xmlRes) {
      return null
    }

    const regex = /"current_level_of_education"\s*:\s*"(低龄|小学|初中|职高|中专|技校|高中|大专|本科|硕士|博士)"/
    const match = xmlRes.match(regex)
    if (match) {
      return match[1]
    }

    return null
  }

  public static async inviteOperationGroup(state: IWorkflowState) {
    const chatState = ChatStateStore.get(state.chat_id)
    const isParent = chatState?.userSlots?.is_parent
    if (chatState?.status?.operationGroupInvited) { // 已经发送过运营群二维码，不再进行发送
      return
    }


    // logger.debug({ chat_id: state.chat_id }, '发送运营群二维码')
    // let operationGroupInvite = '你需要参考顾问和用户的聊天记录，邀请客户进入运营群。注意在邀请之间，先简单回应客户2句，再引导客户进入运营群。参考话术："这样，我先邀请你进入我们的升学群，群里都是跟你一样年纪的同学，群里不定期会有各种关于升学，各个国家的学校的信息，你可以在群里先学习和关注起来，随时在群里问问题，也有老师回复和解答。你也可以随时留言给我，我们都在的，稳了！"'
    //
    // if (isParent)  {
    //   operationGroupInvite = '你需要参考顾问和用户的聊天记录，邀请顾问进入运营群。参考话术："家长，我邀请你进入我们的升学群，群里不定期会有各种关于升学，实习，各个国家的学校的信息，你可以在群里先学习和关注起来，随时在群里问问题，也有老师回复和解答。你也可以随时留言给我，我们都在的，稳了！"'
    // }

    ChatStateStore.update(state.chat_id, {
      state: ConversationState.OperationGroupInvited,
      status: {
        operationGroupInvited: true,
      }
    })

    // // 拉运营群
    // await LLMNode.invoke({
    //   state,
    //   referenceChatHistory: true,
    //   dynamicPrompt: operationGroupInvite,
    //   noInterrupt: true
    // })
    //
    // await InviteToGroupHelper.sendGroupQRCode(state)
  }

  private static includeCounselorFamilyName(userMessage: string) {
    // 示例数据
    const names = [
      '胡老师',
      '吕孟泽',
      '马剑萍',
      '石剑杰',
      '孙博峰',
      '邰东升',
      '王子飏',
      '翁婷',
      '吴丽芳',
      '杨悦',
      '魏清平',
      '杜海祺',
      '国御宣',
      '梁亚洲',
      '汝乐乐',
      '孙天奕',
      '田雪芹',
      '张莉',
      '朱恺',
      '郜天蛟',
      '程文琼',
      '高超超',
      '李朋',
      '楼敏琪',
      '缪沛凌',
      '邱彩燕',
      '郁子易',
      '周晨',
      '周梦迪',
      '周文轩',
      '沈娜',
      '付瑶',
      '秦芳',
      '阙碧',
      '王浩浩',
      '魏子薇',
      '朱阳洋'
    ]


    // 构建去重的姓氏列表
    const surnamesSet = new Set<string>()

    names.forEach((name) => {
      const surname = name.charAt(0) // 提取姓氏
      surnamesSet.add(surname) // 添加到 Set 中以去重
    })

    const surnamesList = Array.from(surnamesSet) // 将 Set 转换为数组


    return surnamesList.some((surname) => userMessage.includes(`${surname  }老师`))
  }
}