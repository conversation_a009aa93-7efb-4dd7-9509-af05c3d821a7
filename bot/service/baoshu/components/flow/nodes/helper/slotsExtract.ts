import { LLM } from '../../../../../../lib/ai/llm/LLM'
import { XMLHelper } from '../../../../../../lib/xml/xml'
import { z } from 'zod'
import { JSONHelper } from '../../../../../../lib/json/json'
import { ObjectUtil } from '../../../../../../lib/object'
import { LLMXMLHelper } from './xmlHelper'
import logger from '../../../../../../model/logger/logger'

export class UserSlotsExtract {
  public static async extract(userSlotsExtractPrompt: string, booleanValueExtractPrompt: string, logInfo?: { chat_id: string; user_id: string; round_id: string }) {
    // 并发调用
    const llm = new LLM({
      meta: {
        ...logInfo,
        promptName: 'UserSlotsExtract',
        description: '提取用户槽位'
      }
    })

    const llmRes = await Promise.all([llm.predict(userSlotsExtractPrompt), llm.predict(booleanValueExtractPrompt)])

    const userSlots = llmRes.map((res) => XMLHelper.extractContent(res, 'extracted_info'))

    try {
      const [userSlotsObj, booleanSlotsObj] = userSlots.map((e) => {
        if (!e) {
          return {}
        } else {
          return JSONHelper.parse(e)
        }
      })
      const slotsObj = ObjectUtil.merge(userSlotsObj, booleanSlotsObj)
      const repairedUserSlots = this.repair(slotsObj)

      const jsonSchema = z.object({
        is_study_abroad: z.string().nullish(),
        is_parent: z.boolean().nullish(),
        application_status: z.enum(['未知', '未开始', '申请中', '已完成']).nullish(),
        budget: z.number().array().or(z.number()).nullish(),
        budget_is_per_year: z.boolean().nullish(),
        gpa: z.string().nullish(),
        language_test_score: z.string().nullish(),
        current_level_of_education: z.enum(['低龄', '小学', '初中', '职高', '中专', '高中', '大专', '本科', '硕士', '博士']).nullish(),
        goal: z.string().nullish(),
        user_intended_country: z.string().array().nullish(),
        application_stage: z.enum(['高中', '大专', '本科', '硕士', '博士']).nullish(),
        preferred_plan: z.string().nullish(),
        city: z.string().nullish(),
        school: z.string().nullish(),
        name:z.string().nullish(),
        phone:z.string().nullish(),
        examination_score:z.number().nullish(),
        is_gaokao_student:z.boolean().nullish()
      })

      const validationResult = jsonSchema.safeParse(repairedUserSlots)
      if(!validationResult.success) {
        logger.error('extractSlots Validation failed', validationResult.error)
      }

      return repairedUserSlots
    } catch (e) {
      console.error('error parsing esQueryJSON', e)
      return null
    }
  }

  private static repair(userSlots: Record<string, any>): Record<string, any> {
    const result: Record<string, any> = {}

    for (const key in userSlots) {
      if (userSlots.hasOwnProperty(key)) {
        let value = userSlots[key]

        // 修复槽位值
        if (key === 'budget') {
          if (typeof value === 'string') {
            value = parseFloat(value)
            if (isNaN(value)) {
              value = null
            }
          }

          if (value) {
            if (Array.isArray(value) && value.length > 1) {
              value = Math.max(...value) // 后续查询的时候，查询最大预算即可
            }

            if (value >= 10000) {
              value = Math.round(value / 10000)
            }
          }
        } else if (typeof value === 'string') {
          value = value.trim()
        }

        // 过滤无效的值
        if (value !== 0 && value !== null && !(Array.isArray(value) && value.length === 0) && (key !== 'application_status' && value !== '未知') && value !== '') {
          result[key] = value
        }
      }
    }

    // 把 current_level_of_education 做下修复
    if (result.grade && !result.current_level_of_education) {
      let current_level_of_education: undefined | string = undefined
      const grade = result.grade as string
      if (['小学之前', '一年级', '二年级', '三年级', '四年级', '五年级', '六年级', '初一'].includes(grade)) {
        current_level_of_education = '低龄'
      } else if (['初二', '初三', '初四'].includes(grade)) {
        current_level_of_education = '初中'
      } else if (['高一', '高二', '高三'].includes(grade)) {
        current_level_of_education = '高中'
      } else if (['大一', '大二', '大三', '大四'].includes(grade)) {
        current_level_of_education = '本科'
      } else if (['研一', '研二', '研三'].includes(grade)) {
        current_level_of_education = '硕士'
      } else if (['博一', '博二', '博三', '博四', '博五', '博六'].includes(grade)) {
        current_level_of_education = '博士'
      }

      if (current_level_of_education) { // 注意，如果本来有值，这里没有修正，这里不进行覆盖
        result.current_level_of_education = current_level_of_education
      }

    }

    if (result.is_gaokao_student) {
      result.current_level_of_education = '高中'
    }

    return result
  }

  static async isParentSupported(prompt: string) {
    const res = await LLMXMLHelper.extractBooleanAnswer(prompt, {
      trueFlag: 'YES',
      falseFlag: 'NO',
    })

    if (res === null) {
      return undefined
    }
    return res
  }

  public static formatSlots(userSlots: any) {
    let userSlotsInfo = ''

    const translateKeyToChinese = (key: string): string => {
      switch (key) {
        case 'is_japanese_student': return '是否是日语学生'
        case 'application_status': return '申请状态'
        case 'budget': return '预算'
        case 'budget_is_per_year': return '预算是每年'
        case 'gpa': return 'GPA'
        case 'language_test_score': return '语言考试成绩'
        case 'current_level_of_education': return '当前学历水平'
        case 'grade': return '年级'
        case 'goal': return '目标'
        case 'user_intended_country': return '意向国家'
        case 'application_stage': return '申请阶段'
        case 'preferred_plan': return '首选方案'
        case 'city': return '城市'
        case 'school': return '学校'
        case 'major': return '专业'
        case 'user_intended_school': return '意向学校'
        case 'user_intended_project': return '意向项目'
        case 'is_study_abroad': return '是否留学'
        case 'is_parent': return '是否为家长'
        case 'name': return '姓名'
        case 'phone': return '电话'
        case 'examination_score': return '高考总分'
        default: return '' // 未知的键忽略
      }
    }

    const formatValueWithUnit = (key: string, value: any): string => {
      if (key === 'budget' && value !== null) {
        return `${value} 万`
      }
      if (key === 'budget_is_per_year' || key === 'is_study_abroad' || key === 'is_parent') {
        return value ? '是' : '否'
      }

      if (key === 'user_intended_country' || key === 'user_intended_school' || key === 'user_intended_project') {
        return (value as string[]).join(', ')
      }
      return `${value}`
    }

    for (const key of Object.keys(userSlots)) {
      if (key === '') continue // 跳过空键
      const chineseKey = translateKeyToChinese(key)
      if (chineseKey === '') continue

      const value = userSlots[key]
      if (!value) continue
      const formattedValue = formatValueWithUnit(key, value)

      userSlotsInfo += `${chineseKey}：${formattedValue}\n`
    }

    return userSlotsInfo.trim()
  }
}