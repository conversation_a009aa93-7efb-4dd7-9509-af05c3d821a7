import { JuziAPI } from '../../../../../../lib/juzi/api'
import { Config } from '../../../../../../config/config'
import logger from '../../../../../../model/logger/logger'


export async function isPastUser(senderId: string) {
  if (senderId === '7881301047907394' || senderId === '7881299536073194' || senderId === '7881300846030208' || senderId === '7881302298050442') {
    return false
  }

  if (senderId === '5629502228673975') { // 销帮帮 CRM 信息
    return true
  }

  if (Config.setting.wechatConfig?.name && !(['syq'].includes(Config.setting.wechatConfig?.name))) {
    if (senderId === Config.setting.wechatConfig?.id) {
      return false
    }

    const userInfo = await JuziAPI.getCustomerInfo(Config.setting.wechatConfig?.id as string, senderId)
    if (!userInfo) {
      logger.warn('用户不存在', senderId)
      return true
    }

    if (['麦子', '哈哈哈', 'SYQ', 'Horus'].includes(userInfo.name)) {
      return false
    }

    const timeLine = '2025-06-21T12:00:00+08:00'
    if (userInfo && (Number(userInfo.createTimestamp) < new Date(timeLine).getTime())) {
      logger.warn(`用户在${timeLine}之前创建`, senderId)
      return true
    }
  }

  return false
}