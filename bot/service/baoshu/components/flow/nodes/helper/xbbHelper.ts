import {  Xbb<PERSON><PERSON>Id, <PERSON><PERSON><PERSON><PERSON>, XiaoBangBangAPI } from '../../../../../../lib/xiaobangbang/xiaobangbang'
import { ChatStateStore } from '../../../../storage/chat_state_store'

import { Config } from '../../../../../../config/config'
import { UserEducationGroup } from '../intentionCalculate'
import { LLM } from '../../../../../../lib/ai/llm/LLM'
import { XMLHelper } from '../../../../../../lib/xml/xml'
import { CityToProvincePrompt } from '../../../../prompt/baoshu/city_to_province'
import { InviteToGroupHelper } from './inviteToGroup'
import { getChatId } from '../../../../../../config/chat_id'

import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from './juzi<PERSON>elper'
import logger from '../../../../../../model/logger/logger'
import { ChatDB } from '../../../../database/chat'
import { MessageReplyService } from '../../../../../message/message_reply'

export async function cityToProvince(city: string) {
  const llm = new LLM({ model: 'gpt-4o-mini' })
  const llmRes = await llm.predict(await CityToProvincePrompt.format(city))
  const res =  XMLHelper.extractContent(llmRes, 'answer')?.trim()
  logger.debug(`cityToProvince: ${city}->${res}`)
  return res
}

export enum ClueStatus {
  WaitingDealing = '待处理', // 待处理
  Following = '跟进中', // 跟进中
  ConvertedToCustomer = '已转换', // 已转化
  InValid = '无效', // 无效客户
}

export class XbbHelper {
  // 从 XbbId 映射表中获取，搜索 XiaoBangBangAPI.getAllMembers() 接口
  private static counselorIdMap = new Map<string, string>([
    ['momo', 'woOGQmBgAA0eS3XUppjTNZLRVqzRcfoA'],
    ['afy-aimee', 'woOGQmBgAAjGC1Piq1MSPtIsN5HT9_Ew'],
    ['GaoFei', 'woOGQmBgAA6yK7yJmZAO9Hqk8htdjWJg'],
    ['<EMAIL>', 'woOGQmBgAA0eS3XUppjTNZLRVqzRcfoA'],
    ['GaoTianJiao', 'woOGQmBgAABX9iGdfcPSCZ7JMiyiG8zg'],
    ['WeiQingPing', 'woOGQmBgAAReZMwNi6RCMryGA27_AOpQ'],
    ['HuRong_1', 'woOGQmBgAAi3qA-7gxhr3zPGLjQw_pEw4'],
    ['JiaJia', 'woOGQmBgAA0OzcUIKBxEwFuE_iKifoLw'],
    ['<EMAIL>', 'woOGQmBgAAoJqKM9MQN3wQlWGfssvwIQ'],
    ['DaiXueJiao', 'woOGQmBgAAgr_oJ1jcKzrK6zJQyMUcxQ'],
    ['ZhangZiHao', 'woOGQmBgAAOAC7KCnlXGcrWWsGIxbyIA'],
    ['WuYuCong', 'woOGQmBgAA2Ib5WL3iiJhtcex3sZznmg'],
    ['LaoYuLei', 'woOGQmBgAAoxPqChUZP_JNbsEuYD6NLA'],
    ['ZhuYangYang', 'woOGQmBgAAM-YzdgUQEKauEmEZG0gQCw'],
    ['QueBi', 'woOGQmBgAALrod1o8frEBLNNxn8_nCng']
  ])

  /**
   * 转换客户
   * @param chat_id
   * @param user_id
   */
  public static async transferClueToCustomer(chat_id: string, user_id: string) {
    if (!Config.setting.localTest && ['欢乐斗地主AFM-ada', '躺着数钱-郜天蛟', '财富自由-pingping白', '百万年薪-Janice', '支持组2(暴叔)'].includes(Config.setting.wechatConfig?.name as string)) {
      // 销帮帮处理逻辑
      const tags = await XbbHelper.getTags(chat_id)
      const userName = await JuziHelper.getWechatNameByUserId(user_id)
      const dbUser = await ChatDB.getById(chat_id)
      let createdTime = new Date()
      if (dbUser) {
        createdTime = dbUser.created_at as Date
      }

      try {
        await XbbHelper.labelUser(userName, tags, XbbFormId.Clue, createdTime)
        // await XbbHelper.addCustomer(userName, user_id, tags)
      } catch (e) {
        console.error(e)
        logger.error(e)
      }
    }
  }

  /**
   * 线索打标
   * @param chat_id
   * @param user_id
   */
  public static async labelClue(chat_id: string, user_id: string) {
    // if (!Config.setting.localTest && (Config.setting.wechatConfig?.name as string).includes('baoshu')) {
    // 销帮帮处理逻辑
    const addTags = await XbbHelper.getTags(chat_id)
    const userName = await JuziHelper.getWechatNameByUserId(user_id)
    const dbUser = await ChatDB.getById(chat_id)
    let createdTime = new Date()
    if (dbUser) {
      createdTime = dbUser.created_at as Date
    }

    try {
      await XbbHelper.labelUser(userName, addTags, XbbFormId.Clue, createdTime)
    } catch (e) {
      console.error(e)
      logger.error(e)
    } finally {
      ChatStateStore.update(chat_id, {
        status: {
          labeledUser: true
        }
      })

      await MessageReplyService.saveChat(chat_id, user_id)
    }
    // }
  }


  public static async labelUser(userName: string, tags: XbbTag[], formId: XbbFormId, createTime: Date) {
    if (!tags.length) {
      return
    }

    // 打标签
    const xbbUser = await this.getLastClueByName(userName, createTime)

    if (!xbbUser) {
      throw new Error(`xbb 找不到用户：${userName}`)
    }

    const xbbUserId = xbbUser.dataId
    const tagIds = this.getTagIdsByTagNames(tags)

    const res = await XiaoBangBangAPI.addLabel(formId, xbbUserId, tagIds)
    if ((res as any).code !== 1) {
      throw new Error('线索打标签失败')
    }
  }

  public static async labelUserById(dataId: number, tags: XbbTag[], formId: XbbFormId) {
    const tagIds = this.getTagIdsByTagNames(tags)

    const res = await XiaoBangBangAPI.addLabel(formId, dataId, tagIds)
    if ((res as any).code !== 1) {
      throw new Error(`客户打标签失败${JSON.stringify(res)}`)
    }
  }

  public static async addCustomer(userName: string, userId: string, counselor: string, tags: XbbTag[]): Promise<void> {
    const customerInfo = await this.getCustomerInfo(userName, userId, counselor)
    // 转换客户
    // 创建客户
    const customer = await XiaoBangBangAPI.createCustomer(customerInfo)

    if ((customer as any).code !== 1) { // code = 1 操作成功
      throw new Error('转换客户失败')
    }

    // 更新标签
    try {
      await XbbHelper.labelUserById((customer as any).result.dataId, tags, XbbFormId.Customer)
    } catch (e) {
      throw new Error(`客户添加标签失败:${e}`)
    }

  }

  private static async getCustomerInfo(user_name:string, user_id: string, counselor: string) {
    const chatState = ChatStateStore.get(getChatId(user_id))
    const status = ChatStateStore.getStatus(getChatId(user_id))
    const userSlots = chatState.userSlots
    const groupName = await InviteToGroupHelper.getGroupName(user_id)

    let createdBy = this.counselorIdMap.get(counselor) as string
    if (!createdBy) {
      createdBy = 'woOGQmBgAA0eS3XUppjTNZLRVqzRcfoA'
    }

    let isInvitedToGroup: '不同意进群' | '已拉咨询群' | '已拉运营群' = '不同意进群'
    if (status.consultGroupEntered) {
      isInvitedToGroup = '已拉咨询群'
    } else if (status.operationGroupEntered) {
      isInvitedToGroup = '已拉运营群'
    }

    // 顾问Id
    const counselorXbbId: string[] = []
    const counselorId = chatState.counselor_id
    if (counselorId) {
      const counselor = this.counselorIdMap.get(counselorId)

      if (counselor) {
        counselorXbbId.push(counselor as string)
      }
    }

    // 升学阶段
    let educationStage: '其他' | '低龄' | '本科' | '硕士生' | '博士' = '其他'
    if (userSlots && userSlots.education_group) {
      const nextEducationLevelProject = {
        [UserEducationGroup.BelowJuniorOne]: '低龄',
        [UserEducationGroup.JuniorTwoToFour]: '低龄',
        [UserEducationGroup.HighSchool]: '本科',
        [UserEducationGroup.College]: '本科',
        [UserEducationGroup.University]: '硕士生',
        [UserEducationGroup.Gaokao]: '本科',
        [UserEducationGroup.JapanHighSchool]: '本科'
      }

      educationStage = nextEducationLevelProject[userSlots.education_group] || '其他'
    }
    const isParent: '学生' | '家长' = userSlots?.is_parent ? '家长' : '学生'

    let budgetRange: '其他' | '10-20万' | '20-30万' | '30-50万' | '50万以上' = '其他'

    if (userSlots && userSlots.budget) {
      if (userSlots.is_budget_per_year) {
        const budget = userSlots.budget
        // '10-20万' | '20-30万' | '30-50万' | '50万以上' | '其他' // 预算（每年）
        if (budget >= 10  && budget <= 20) {
          budgetRange = '10-20万'
        } else if (budget > 20 && budget <= 30) {
          budgetRange = '20-30万'
        } else if (budget > 30 && budget <= 50) {
          budgetRange = '30-50万'
        } else if (budget > 50) {
          budgetRange = '50万以上'
        } else {
          budgetRange = '其他'
        }
      } else {
        if (userSlots.budget > 100) {
          budgetRange = '50万以上'
        }
      }
    }

    return {
      text_36: groupName,
      text_16: createdBy,
      text_54: isInvitedToGroup,
      ownerId: counselorXbbId,
      text_53: educationStage,
      text_50: isParent,
      text_1: user_name,
      text_52: budgetRange
    }
  }

  static async getTags(chat_id: string) {
    const userSlots = ChatStateStore.get(chat_id).userSlots
    const tags: XbbTag[] = []

    if (!userSlots) {
      return []
    }

    // 根据用户槽位计算标签

    // 初筛人员
    if (Config.setting.wechatConfig) {
      const counselorGroupMap =  {
        '欢乐斗地主AFM-ada': 'AFM组',
        '躺着数钱-郜天蛟': '躺着数钱组',
        '财富自由-pingping白': '财富自由组',
        '百万年薪-Janice': '百万年薪组',
        '支持组2(暴叔)': '支持组',
        'baoshu7': '躺着数钱组',
        'baoshu8': 'AFM组',
      }

      if (!counselorGroupMap[Config.setting.wechatConfig.name]) {
        // throw new Error(`找不到对应的初筛销售组：${Config.setting.wechatConfig.name}`)
        logger.warn(`找不到对应的初筛销售组：${Config.setting.wechatConfig.name}`)
      }

      tags.push({
        group: '初筛销售',
        name: counselorGroupMap[Config.setting.wechatConfig.name] ?? '支持组'
      })
    }

    // 产品
    if (userSlots.education_group) {
      let product

      const nextEducationLevelProject = {
        [UserEducationGroup.BelowJuniorOne]: '低龄规划',
        [UserEducationGroup.JuniorTwoToFour]: '学分课',
        [UserEducationGroup.HighSchool]: '本科留学',
        [UserEducationGroup.College]: '本科留学',
        [UserEducationGroup.University]: '硕士留学',
        [UserEducationGroup.Gaokao]: '本科留学',
        [UserEducationGroup.JapanHighSchool]: '本科留学'
      }

      product = nextEducationLevelProject[userSlots.education_group]

      if (userSlots.application_stage) {
        // 注意 esObject.application_stage 不一定准确，必须 > 当前教育阶段才算有效
        if (['本科', '硕士', '高中'].includes(userSlots.application_stage)) {
          const applicationStageIndex = {
            '高中': '学分课',
            '本科': '本科留学',
            '硕士': '硕士留学'
          }

          const applicationProjectMap = {
            '本科': '本科留学',
            '硕士': '硕士留学',
            '高中': '本科留学'
          }

          if (applicationStageIndex[userSlots.application_stage] > userSlots.education_group) {
            product = applicationProjectMap[userSlots.application_stage]
          }
        }
      }

      tags.push({
        group: '产品',
        name: product
      })
    }

    // 省份
    if (userSlots.city) {
      const province = await cityToProvince(userSlots.city)

      if (province) {
        tags.push({
          group: '省份',
          name: province
        })
      }
    }

    if (userSlots.user_intended_country) {
      if (Array.isArray(userSlots.user_intended_country)) {
        for (let country of userSlots.user_intended_country) {
          if (country === '香港' || country === '澳门') {
            country = '港澳'
          }

          const countries: string[] = ['美国', '英国', '加拿大', '澳洲', '新西兰', '新加坡', '马来西亚', '港澳', '韩国', '日本', '俄罗斯', '北欧', '德国', '芬兰', '意大利', '泰国', '西班牙', '法国']

          if (countries.includes(country)) {
            tags.push({
              group: '意向国家',
              name: country
            })
          }
        }
      }
    }

    if (userSlots.grade) {
      const grades = [
        '初一', '初二', '初三',
        '高一', '高二', '高三',
        '大一', '大二', '大三', '大四',
        '研一', '研二'
      ]
      if (grades.includes(userSlots.grade)) {
        tags.push({
          group: '目前年级',
          name: userSlots.grade
        })
      } else {
        if (userSlots.education_group) {
          const nextEducationLevelProject = {
            [UserEducationGroup.BelowJuniorOne]: '小学',
            [UserEducationGroup.JuniorTwoToFour]: '初中毕业',
            [UserEducationGroup.HighSchool]: '高中毕业',
            [UserEducationGroup.College]: '大专',
            [UserEducationGroup.University]: '本科毕业',
            [UserEducationGroup.Gaokao]: '高中毕业',
            [UserEducationGroup.JapanHighSchool]: '高中毕业'
          }

          const grade = nextEducationLevelProject[userSlots.education_group]
          if (grade) {
            tags.push({
              group: '目前年级',
              name: grade
            })
          }
        }
      }
    }

    // 客户情况
    if (userSlots.is_parent) {
      tags.push({
        group: '客户情况',
        name: '家长'
      })
    } else {
      tags.push({
        group: '客户情况',
        name: '学生'
      })
    }



    return tags
  }

  static getTagIdsByTagNames(tags: XbbTag[]): number[] {
    const tagsMap =  {
      'B端机构信息': {
        '个人工作室': 118895,
        '中型机构': 118896,
        '集团型机构': 118897,
        '留学机构': 118898,
        '培训机构': 118899,
        '移民机构': 118900,
        '国际学校': 118901,
        'To B机构': 118902,
        '顾问': 118903,
        '老板': 118904,
        '文案': 118905,
        '主管': 118906
      },
      '初筛销售': {
        '程勇': 118860,
        'Janice': 118861,
        '国老师': 118862,
        '张莉': 118865,
        '张梦': 118866,
        '宋美玉': 118867,
        'Elsa': 118868,
        'Ada': 118870,
        '郜老师': 118871,
        '马剑萍': 118872,
        '贾佳': 118873,
        '田雪芹': 118874,
        '小白': 118875,
        'Cherry': 118877,
        'UE': 118879,
        'janice zhou': 118880,
        'Vicky': 119317,
        '邱老师': 121457,
        'Anny': 122255,
        '吕金萍': 125694,
        'Grace': 129972,
        'Vivi': 129973,
        '陈琳': 130146,
        '杨悦': 131208,
        '梁亚洲': 134558,
        '戴鹰': 140054,
        '翁婷': 140134,
        '沈俊': 141239,
        '叶晨博': 141240,
        '姜维生': 141241,
        '赵万亮': 141242,
        '杨帅': 141243,
        '李寅亮': 142148,
        '赵亚男': 142385,
        '王浩浩': 142529,
        '夏紫薇': 142624,
        'AFM组': 143637,
        '百万年薪组': 143635,
        '财富自由组': 143636,
        '躺着数钱组': 143638,
        '高超超': 144370,
        '支持组': 144734
      },
      '未回复': {
        '未回复': 124064,
        '待聊（高考季）': 136315
      },
      '产品': {
        '语培': 118514,
        '作品集': 118515,
        '背景提升': 118516,
        '实习就业': 118517,
        '竞赛': 118518,
        'GPA管理': 118519,
        'DSE': 118520,
        '学分课': 118521,
        '合作办学4+0': 118522,
        '合作办学硕士': 118523,
        '国际本科': 118524,
        '文书产品': 118525,
        '博士申请': 118526,
        'AST': 118527,
        '优才计划': 118528,
        '低龄规划': 118529,
        '规划服务': 118530,
        '本科留学': 125523,
        '硕士留学': 125524,
        '技术类': 125558,
        'OD': 141669
      },
      '省份': {
        '北京': 118483,
        '上海': 118484,
        '天津': 118485,
        '重庆': 118486,
        '河北': 118487,
        '山西': 118488,
        '黑龙江': 118489,
        '吉林': 118490,
        '辽宁': 118491,
        '江苏': 118492,
        '浙江': 118493,
        '安徽': 118494,
        '福建': 118495,
        '江西': 118496,
        '山东': 118497,
        '河南': 118498,
        '湖北': 118499,
        '湖南': 118500,
        '广东': 118501,
        '海南': 118502,
        '四川': 118503,
        '贵州': 118504,
        '云南': 118505,
        '陕西': 118506,
        '甘肃': 118507,
        '青海': 118508,
        '内蒙古': 118509,
        '广西': 118510,
        '宁夏': 118511,
        '新疆': 118512,
        '海外': 118513
      },
      '意向国家': {
        '美国': 118464,
        '英国': 118465,
        '加拿大': 118466,
        '澳洲': 118467,
        '新西兰': 118468,
        '新加坡': 118469,
        '马来西亚': 118470,
        '港澳': 118471,
        '韩国': 118472,
        '日本': 118473,
        '俄罗斯': 118474,
        '北欧': 118475,
        '德国': 118476,
        '芬兰': 118477,
        '意大利': 118478,
        '泰国': 118479,
        '西班牙': 118480,
        '法国': 118481
      },
      '目前年级': {
        '幼儿园': 118447,
        '小学': 118448,
        '初一': 118449,
        '初二': 118450,
        '初三': 118451,
        '高一': 118452,
        '高二': 118453,
        '高三': 118454,
        '大一': 118455,
        '大二': 118456,
        '大三': 118457,
        '大四': 118458,
        '研一': 118459,
        '研二': 118460,
        '中专': 118461,
        '大专': 118462,
        '博士': 118463,
        '其他': 119339,
        '本科（自考/函授等）': 121039,
        '初中毕业': 125525,
        '高中毕业': 125526,
        '本科毕业': 125527
      },
      '客户情况': {
        '当季': 118438,
        '非当季': 118439,
        '学生': 118440,
        '家长': 118441,
        '男': 118442,
        '女': 118443,
        '有其他服务机构': 118444
      },
      '预算情况': {
        '高预算': 143639,
        '中预算': 143640,
        '普通预算': 143641,
        '高认知': 143642,
        '中认知': 143643,
        '普通认知': 143644
      }
    }

    const tagIds: number[] = []

    for (const tag of tags) {
      if (tagsMap[tag.group][tag.name]) {
        tagIds.push(tagsMap[tag.group][tag.name] as number)
      } else {
        logger.warn(`找不到标签：${tag.group} - ${tag.name}`)
      }
    }

    return tagIds
  }

  private static async changeClueStatus(userName: string,  status: ClueStatus) {
    const xbbUser = await XiaoBangBangAPI.getCluesByName(userName)

    if (!xbbUser) {
      throw new Error(`xbb 找不到用户：${userName}`)
    }

    const xbbUserId = xbbUser.dataId

    const res = await XiaoBangBangAPI.editClue(xbbUserId, {
      text_5: ClueStatus.ConvertedToCustomer
    })
    if ((res as any).code !== 1) {
      throw new Error('修改线索状态失败')
    }
  }

  public static async getLastClueByName(userName: string, createTime: Date) {
    const users = await XiaoBangBangAPI.getCluesByName(userName)
    if (!users || !Array.isArray(users)) {
      return null
    }

    if (users.length === 0) {
      return null
    } else  {
      // 取出离 createTime 最接近的客户
      const createTimeSecondTimestamp = createTime.getTime() / 1000

      const closestUser = users
        .sort((a, b) => Math.abs(a.addTime - createTimeSecondTimestamp) - Math.abs(b.addTime - createTimeSecondTimestamp))

      return closestUser[0]
    }
  }
}