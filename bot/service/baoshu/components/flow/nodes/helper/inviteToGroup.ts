import { IWorkflowState } from '../../flow'
import { LLM } from '../../../../../../lib/ai/llm/LLM'
import { isUserAgreedToJoinConsultGroupPrompt } from '../../../../prompt/baoshu/confirm'
import { XMLHelper } from '../../../../../../lib/xml/xml'
import chalk from 'chalk'
import { JuziAPI } from '../../../../../../lib/juzi/api'
import { Config } from '../../../../../../config/config'
import { WechatMessageSender } from '../../../message_send'
import { ChatStateStore, ConversationState } from '../../../../storage/chat_state_store'
import { BaoshuNode } from '../../type'
import { LLMNode } from '../llm'
import { <PERSON>Trans<PERSON>, HumanTransferType } from '../../../human_transfer'
import { IWecomMsgType } from '../../../../../../lib/juzi/type'
import { IntentionCalculateNode, UserEducationGroup } from '../intentionCalculate'
import { ChatHistoryService } from '../../../chat_history'
import { trackInvoke } from '../baseNode'
import { JuziHelper } from './juziHelper'
import logger from '../../../../../../model/logger/logger'
import { sleep } from '../../../../../../lib/schedule/schedule'
import { XbbHelper } from './xbbHelper'
import { ABTest } from '../../../../../../model/a_btest/a_b_test'
import { DailyCounter } from './countByDate'
import { RegexHelper } from '../../../../../../lib/regex/regex'
import { EndNode } from '../end'


export class InviteToGroupHelper {

  @trackInvoke
  public static async handleEnterGroup(state: IWorkflowState) {
    const status = ChatStateStore.getStatus(state.chat_id)
    if (status.operationGroupInvited) {
      const chatHistory = await ChatHistoryService.formatHistory(state.chat_id)
      if (!chatHistory.includes('进群二维码')) {
        await this.sendGroupQRCode(state)
      }

      return BaoshuNode.END
    }

    const chatState = ChatStateStore.get(state.chat_id)
    const userSlots = chatState.userSlots
    // 符合条件的大一进运营群
    if (userSlots && userSlots.grade === '大一') {
      await IntentionCalculateNode.inviteOperationGroup(state)
      return BaoshuNode.END
    }

    // 根据过去的对话，判断用户是否同意进群
    const nodeRound = ChatStateStore.getNodeEnterCount(state.chat_id, InviteToGroupHelper.name)

    const isAgree  = await InviteToGroupHelper.isUserAgreeToEnterGroup(state) // 'true' | 'false' | 'not yet'
    logger.debug({ chat_id: state.chat_id }, chalk.redBright(`用户是否同意进群：${ isAgree }`))

    if (isAgree === 'true' || (nodeRound >= 1 && isAgree !== 'false')) { // 多问一轮后，用户仍然没有明确拒绝，直接强行拉群
      await this.inviteToCounselor(state)
      return BaoshuNode.END
    } else if (isAgree === 'not yet') {
      // 如果已经邀请过，不进行回复
      const chatHistory = await ChatHistoryService.formatHistory(state.chat_id)
      if (chatHistory.includes('咱们真考虑出去的话，我给你安排个专业方向很强的老师给你具体规划下')) {
        if (!chatHistory.includes('咱们的情况需要进一步的询问和讨论。我们一句两句也说不太清楚，我还是建议让专业的顾问老师跟你沟通下，聊聊是免费的，了解清楚了才能给你最佳的方案。稍等。我拉群给你。')) {
          await WechatMessageSender.sendById({
            user_id: state.user_id,
            chat_id: state.chat_id,
            ai_msg: '咱们的情况需要进一步的询问和讨论。我们一句两句也说不太清楚，我还是建议让专业的顾问老师跟你沟通下，聊聊是免费的，了解清楚了才能给你最佳的方案。稍等。我拉群给你。'
          })
        }

        await this.inviteToCounselor(state)
        return BaoshuNode.END
      } else {
        // 用户未同意进群，可以再询问下用
        // 户还有什么问题，然后再次邀请
        await LLMNode.invoke({
          state,
          dynamicPrompt: '你当前已经邀请过用户进入顾问群，用户仍处于没有直接同意或犹豫的状态，你可以先回答用户问题， 然后以你很忙，时间不够的方式，让顾问老师帮助规划的方式再次引导用户进群。或者参考这个话术："咱们的情况需要进一步的询问和讨论。我们一句两句也说不太清楚，我还是建议让专业的顾问老师跟你沟通下，聊聊是免费的，了解清楚了才能给你最佳的方案。稍等。我拉群给你。"',
          referenceChatHistory: true,
        })
      }

      return BaoshuNode.InviteConsultant
    } else {
      const chatState = ChatStateStore.get(state.chat_id)
      const isParent = chatState?.userSlots?.is_parent
      const budget = chatState?.userSlots?.budget || 0
      if (budget > 10 || isParent) {
        logger.debug('发送运营群二维码')
        let operationGroupInvite = '你需要参考顾问和用户的聊天记录，邀请顾问进入运营群。参考话术："可以的，同学，我理解。我邀请你进入我们的(本科，硕士）升学群，群里都是跟你一样年纪的同学，群里不定期会有各种关于升学，实习，各个国家的学校的信息，你可以在群里先学习和关注起来，随时在群里问问题，也有老师回复和解答。你也可以随时留言给我，我们都在的，稳了！"'

        if (isParent)  {
          operationGroupInvite = '你需要参考顾问和用户的聊天记录，邀请顾问进入运营群。参考话术："可以的，家长，我理解。我邀请你进入我们的(本科，硕士）升学群，群里不定期会有各种关于升学，实习，各个国家的学校的信息，你可以在群里先学习和关注起来，随时在群里问问题，也有老师回复和解答。你也可以随时留言给我，我们都在的，稳了！"'
        }

        ChatStateStore.update(state.chat_id, {
          state: ConversationState.OperationGroupInvited,
          status: {
            operationGroupInvited: true,
          }
        })

        await EndNode.invoke(state)

        // // 拉运营群
        // await LLMNode.invoke({
        //   state,
        //   referenceChatHistory: true,
        //   dynamicPrompt: operationGroupInvite,
        //   noInterrupt: true
        // })
        //
        // await this.sendGroupQRCode(state)

        ChatStateStore.update(state.chat_id, {
          state: ConversationState.OperationGroupInvited,
          status: {
            operationGroupInvited: true,
            operationGroupEntered: true,
          }
        })

        return BaoshuNode.END
      } else {
        // 用户不同意进群，结束对话
        await LLMNode.invoke({
          state,
          dynamicPrompt: '当前用户不同意进群，礼貌的结束对话'
        })

        return BaoshuNode.END
      }
    }
  }

  public static async sendGroupQRCode(state: IWorkflowState) {
    // const chatState = ChatStateStore.get(state.chat_id)
    // const userSlots = chatState.userSlots
    //
    // let qrCode = 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/baoshu/qrcode/university.jpg'
    // if (userSlots && (userSlots.education_group === UserEducationGroup.HighSchool || userSlots.education_group === UserEducationGroup.JuniorTwoToFour || userSlots.education_group === UserEducationGroup.BelowJuniorOne)) {
    //   if (userSlots.is_parent) {
    //     qrCode = 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/baoshu/qrcode/university_parent.jpg'
    //   } else if (userSlots.has_offer) {
    //     qrCode = 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/baoshu/qrcode/master.jpg'
    //   } else if (userSlots.has_agent) {
    //     qrCode = 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/baoshu/qrcode/master.jpg'
    //   } else {
    //     qrCode = 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/baoshu/qrcode/university.jpg'
    //   }
    // } else if (userSlots && (userSlots.education_group === UserEducationGroup.College || userSlots.education_group === UserEducationGroup.University || userSlots.application_stage === '硕士')) {
    //   qrCode = 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/baoshu/qrcode/master.jpg'
    // }
    //
    // await WechatMessageSender.sendById({
    //   user_id: state.user_id,
    //   chat_id: state.chat_id,
    //   ai_msg: '[[进群二维码]]',
    //   send_msg: {
    //     type: IWecomMsgType.Image,
    //     url: qrCode
    //   }
    // })
  }


  public static async isUserAgreeToEnterGroup(state: IWorkflowState) {
    const llm = new LLM()
    // 这里用最后一轮数据作为参考
    const lastRound = await ChatHistoryService.getRecentConversations(state.chat_id, 2)
    const messages = ChatHistoryService.formatHistoryHelper(lastRound)
    const llmRes = await llm.predict(await isUserAgreedToJoinConsultGroupPrompt.format(messages))

    // 提取出 answer 的标签中，true or false
    const xmlAnswer = XMLHelper.extractContent(llmRes, 'answer')

    if (!xmlAnswer) {
      logger.debug({ chat_id: state.chat_id }, 'isUserAgreedToJoinConsultGroupPrompt 提取 answer 失败', llmRes)
      return 'not yet'
    }

    if(['true', 'false', 'not yet'].includes(xmlAnswer)) {
      return xmlAnswer
    } else {
      logger.error('isUserAgreeToEnterConsultGroup Validation failed!', xmlAnswer)
      return 'not yet'
    }
  }

  public static async inviteToCounselor(state: IWorkflowState, earlyAge = false) {
    const status  = ChatStateStore.getStatus(state.chat_id)

    // 防止重复拉群
    if (status.consultGroupEntered || status.operationGroupEntered) {
      return BaoshuNode.END
    }

    // 更新进群状态
    ChatStateStore.update(state.chat_id, {
      state: ConversationState.ConsultGroupEntered,
      status: {
        consultGroupEntered: true
      }
    })

    try {
      const externalUserId = await JuziAPI.wxIdToExternalUserId(state.user_id)
      if (!externalUserId) {
        throw new Error('获取 ExternalUserId 失败')
      }

      const { counselor } = await this.getInvitedCounselor(state)
      const groupName = await this.getGroupName(state.user_id)

      const { data } = await JuziAPI.createRoom({
        botUserId: Config.setting.wechatConfig?.botUserId as string,
        userIds: [counselor],
        name: groupName,
        greeting: '咱们在这里',
        externalUserIds: [externalUserId]
      })

      // 发送聊天记录 到群里，不影响主流程
      try {
        const userInfo = await JuziAPI.getCustomerInfo(Config.setting.wechatConfig?.id as string, state.user_id)
        let name = '客户：'
        if (userInfo) {
          name = userInfo.name
        }

        // 发送格式
        const chat_history = `${(await ChatHistoryService.formatHistory(state.chat_id)).replace(/客户：/g, `${name}：`)}`
        const send_msg = `聊天记录：

${chat_history}
- 由企微微工具生成`

        await sleep(5000)

        await WechatMessageSender.sendById({
          room_id: data.roomWxid,
          chat_id: state.chat_id,
          ai_msg: send_msg
        }, false)
      } catch (e) {
        logger.error('发送聊天记录失败', e)
      }

      await sleep(5000)

      if (earlyAge) {
        await WechatMessageSender.sendById({
          room_id: data.roomWxid,
          chat_id: state.chat_id,
          ai_msg: '家长， 我已经安排了我们低龄规划的专家给你。 老师会跟孩子做一个专业的家庭测评，再看怎么一对一定制规划。放心，都稳啦！ '
        }, false)
      } else {
        await WechatMessageSender.sendById({
          room_id: data.roomWxid,
          chat_id: state.chat_id,
          ai_msg: '有什么问题都可以咨询暴叔团队的专业老师。可以的话麻烦留下 姓名+电话，老师可以直接给咱们打电话，沟通更高效。'
        }, false)

        await sleep(5000)

        function isCurrentTimeBetweenMidnightAnd5AM (): boolean {
          const now = new Date ()
          const hours = now.getHours ()
          return hours >= 0 && hours <= 5
        }

        await WechatMessageSender.sendById({
          room_id: data.roomWxid,
          chat_id: state.chat_id,
          ai_msg: isCurrentTimeBetweenMidnightAnd5AM() ?  '比较晚的话，老师明天联系咱们。有时候老师可能在电话或者实地咨询中，回复不及时还请见谅。 但是看到后会马上第一时间回复！' : '有时候老师可能在电话或者实地咨询中，回复不及时还请见谅。 但是看到后会马上第一时间回复！'
        }, false)
      }

      ChatStateStore.update(state.chat_id, {
        counselor_id: counselor
      })

      await sleep(4000)

      const counselorWxIdMap = {
        'ZhangZiHao': '1688855833489166',
        'HuRong_1': '1688854611469274',
        'ZhaiShuai': '1688858168481824',
        'ZhuYangYang': '1688857131482878',
        'QueBi': '1688858345585865',
        'GaoTianJiao': '1688857681495061',
        'WeiQingPing': '1688855884406713',
        'LaoYuLei': '1688855662665360',
        'WuYuCong': '1688857407564471',
        'momo': '1688857745505250'
      }

      await WechatMessageSender.sendById({
        room_id: data.roomWxid,
        chat_id: state.chat_id,
        ai_msg: '老师，麻烦看一下',
        mention: counselorWxIdMap[counselor] ? [counselorWxIdMap[counselor]] : counselorWxIdMap[counselor]
      }, false)

      await WechatMessageSender.sendById({
        user_id: state.user_id,
        chat_id: state.chat_id,
        ai_msg: '好的，已经拉咱进群了，有什么问题都可以直接问专业老师',
      })

      XbbHelper.transferClueToCustomer(state.chat_id, state.user_id) // 异步处理
    } catch (e) {
      logger.error('拉群失败', e)
      // 通知进群失败
      await HumanTransfer.transfer(state.chat_id, state.user_id, HumanTransferType.FailedToJoinGroup)
    }
  }

  private static async getInvitedCounselor(state: IWorkflowState) {
    const counselorGroup =  (Config.setting.wechatConfig?.counselorIds ?? []) as string[]

    const chatState = ChatStateStore.get(state.chat_id)
    const userSlots = chatState.userSlots
    if (!userSlots) {
      throw new Error('获取 userSlots 失败')
    }

    // 规则 1：初一及以下，一律邀请 Janice
    if (userSlots.education_group === UserEducationGroup.BelowJuniorOne) {
      return { counselor: '<EMAIL>' }
    }

    // 账号类型：公域直播 vs 各组直播
    // 公域直播：全局 ABCABCABCD 轮转（4 个账号）
    // 各组直播：沿用当前逻辑，从 counselorGroup 中分配（已绑定到账号）

    // 公域直播候选账号（ABCD）
    const PUBLIC_LIVE_ACCOUNTS = ['GaoTianJiao', 'HuRong_1', 'WeiQingPing', '<EMAIL>']

    const isPublicLiveAccount = Boolean(counselorGroup.length === 0)

    if (isPublicLiveAccount) {
      // 使用 Redis 原子递增保障分布式全局顺序
      const { RedisCacheDB } = await import('../../../../../../model/redis/redis_cache')
      const key = 'baoshu:public_live:global_rr_counter'
      const counter = new RedisCacheDB(key)
      const current = await counter.incr() // 从 1 开始
      const idx = (current - 1) % 10 // 0..9
      const pattern = [0, 1, 2, 0, 1, 2, 0, 1, 2, 3] // ABCABCABCD
      const selected = PUBLIC_LIVE_ACCOUNTS[pattern[idx]]
      return { counselor: selected }
    }

    // 各组直播（默认）：按账号绑定 counselorIds 分配
    const counselors = counselorGroup
    if (counselors.length >= 1) {
      return { counselor: counselors[0] }
    }

    // 兜底（未配置时，不应发生）：随机挑选
    return { counselor: 'HuRong_1' }
  }

  public static async getGroupName(userId: string) {
    const date = new Date()
    const month = date.getMonth () + 1
    const day = date.getDate ()

    // 如果是一位数字，则在前面补 0
    const monthStr = month < 10 ? `0${month}` : String(month)
    const dayStr = day < 10 ? `0${day}` : String(day)
    const dateStr = `${monthStr}${dayStr}`

    let name = await JuziHelper.getWechatNameByUserId(userId)
    name = name.slice(0, 7)
    // 名称要移除 emoji，否则会出问题
    name = name.replaceAll(RegexHelper.toGlobalRegex(RegexHelper.NOT_COMMON_CHAR), '')

    return `${dateStr} ${name} 暴叔留学规划沟通群`
  }


  public static async getCounselorGroupInGaoKaoPeriod() {
    const group0 = [
      { userName: '翁婷', wecomUserId: 'WengTing' },
      { userName: '吴丽芳', wecomUserId: 'WuLiFang' },
      { userName: '马剑萍', wecomUserId: 'MaJianPing' },
      { userName: '杨悦', wecomUserId: 'YangYue' },
      { userName: '钱瑞豪', wecomUserId: 'AFY-xs21' },
      { userName: '诸葛朦', wecomUserId: 'ZhuGeMeng' },
      { userName: '石剑杰', wecomUserId: 'ShiJianJie' },
      { userName: '邱彩燕', wecomUserId: 'QiuCaiYan' },
      { userName: '郁子易', wecomUserId: 'YuZiYi' },
      { userName: '魏清平', wecomUserId: 'WeiQingPing' },
      { userName: '程文琼', wecomUserId: 'ChengWenQiong' },
      { userName: '李朋', wecomUserId: 'LiPeng' },
      { userName: '张莉', wecomUserId: 'ZhangLi' },
      { userName: '周文轩', wecomUserId: 'ZhouWenXuan' },
      { userName: '高超超', wecomUserId: 'GaoChaoChao' },
      { userName: '张子豪', wecomUserId: 'ZhangZiHao' },
      { userName: '宋怡璠', wecomUserId: 'AFY-Ronnie' },
      { userName: '王丽娜', wecomUserId: 'AFY-xs26' },
      { userName: '劳郁蕾', wecomUserId: 'LaoYuLei' },
      { userName: '吴宇聪', wecomUserId: 'WuYuCong' },
      { userName: '沈娜', wecomUserId: '<EMAIL>' }
    ]
    const group1 = [
      { userName: '王子飏', wecomUserId: 'WangZiYang' },
      { userName: '吕孟泽', wecomUserId: 'LvMengZe' },
      { userName: '祝垚垚', wecomUserId: 'ZhuYaoYao' },
      { userName: '蔡万刚', wecomUserId: 'CaiWanGang' },
      { userName: '郭朗', wecomUserId: 'AFY-xs24' }
    ]
    const group2 = [
      { userName: '陶邓为', wecomUserId: 'TaoDengWei' },
      { userName: '易仕杭', wecomUserId: 'YiShiHang' },
      { userName: '方晗之', wecomUserId: 'FangHanZhi' },
      { userName: '阙碧', wecomUserId: 'QueBi' }
    ]

    // 获取选择的组别
    const groupSelectMap = {
      0: 'groupA',
      1: 'groupB',
      2: 'groupC',
      3: 'groupA',
      4: 'groupB',
      5: 'groupA',
    }

    const groupSelectedKey = 'groupSelectedCount'
    const groupSelectCount = await DailyCounter.getCount(groupSelectedKey)
    const selectedIndex = groupSelectCount % 77

    let selectedGroup = 'groupA'

    if ((selectedIndex >= 0 && selectedIndex <= 20) || (selectedIndex >= 30 && selectedIndex <= 50) || (selectedIndex >= 56 && selectedIndex <= 66)) {
      selectedGroup = 'groupA'
    } else if ((selectedIndex >= 21 && selectedIndex <= 25) || (selectedIndex >= 51 && selectedIndex <= 55)) {
      selectedGroup = 'groupB'
    } else if (selectedIndex >= 26 && selectedIndex <= 29) {
      selectedGroup = 'groupC'
    }

    await DailyCounter.incrementCount(groupSelectedKey)


    if (selectedGroup === 'groupA') {
      const key = 'groupACount'
      const groupACount = await DailyCounter.getCount('groupACount')
      const selectedIndex = groupACount % group0.length
      const selectedCounselor = group0[selectedIndex].wecomUserId
      await DailyCounter.incrementCount(key)

      console.log('selectedGroup:', selectedGroup)
      console.log('selectedIndex:', selectedIndex, group0[selectedIndex])
      return selectedCounselor
    } else if (selectedGroup === 'groupB') {
      const key = 'groupBCount'
      const groupBCount = await DailyCounter.getCount('groupBCount')
      const selectedIndex = groupBCount % group1.length
      const selectedCounselor = group1[selectedIndex].wecomUserId
      await DailyCounter.incrementCount(key)

      console.log('selectedGroup:', selectedGroup)
      console.log('selectedIndex:', selectedIndex, group0[selectedIndex])
      return selectedCounselor
    } else {
      const key = 'groupCCount'
      const groupCCount = await DailyCounter.getCount('groupCCount')
      const selectedIndex = groupCCount % group2.length
      const selectedCounselor = group2[selectedIndex].wecomUserId
      await DailyCounter.incrementCount(key)

      console.log('selectedGroup:', selectedGroup)
      console.log('selectedIndex:', selectedIndex, group0[selectedIndex])
      return selectedCounselor
    }




  }
}
