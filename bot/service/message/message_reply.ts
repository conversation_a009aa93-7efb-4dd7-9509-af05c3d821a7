import { Config } from '../../config/config'
import { BaoshuFlow } from '../baoshu/components/flow/flow'
import { ChatHistoryService } from '../baoshu/components/chat_history'
import { WechatMessageSender } from '../baoshu/components/message_send'
import { ChatDB } from '../baoshu/database/chat'
import { getChatId, getUserId } from '../../config/chat_id'
import { ChatStateStore, ChatStatStoreManager, ConversationState } from '../baoshu/storage/chat_state_store'
import { HumanTransfer, HumanTransferType, WecomTag } from '../baoshu/components/human_transfer'
import { InterruptError } from '../baoshu/components/interrupt_handler'
import { JuziAPI } from '../../lib/juzi/api'
import chalk from 'chalk'
import { isPastUser } from '../baoshu/components/flow/nodes/helper/pastUser'
import logger from '../../model/logger/logger'
import { PrismaMongoClient } from '../../model/mongodb/prisma'
import { delayFc, sleep } from '../../lib/schedule/schedule'
import { XbbHelper } from '../baoshu/components/flow/nodes/helper/xbbHelper'
import { Queue, Worker } from 'bullmq'
import { RedisDB } from '../../model/redis/redis'
import { DateHelper } from '../../lib/date/date'
import { GlobalMessageHandlerService } from './message_merge'


export class MessageReplyService {

  public static async reply(text: string[], senderId: string) {
    const chat_id = getChatId(senderId)
    try {
      if (!text.join('').trim()) {
        return
      }

      if (await isPastUser(senderId)) {
        return
      }

      const userMessage = text.join('\n')

      if (userMessage.toLowerCase().includes('clear')) {
        await ChatHistoryService.clearChatHistory(chat_id)
        await ChatStatStoreManager.clearState(chat_id)
        await ChatDB.updateState(chat_id, ChatStateStore.get(chat_id))

        await WechatMessageSender.sendById({
          user_id: senderId,
          chat_id: chat_id,
          ai_msg: '聊天已重置'
        })

        if (await ChatDB.getById(chat_id)) {
          await ChatDB.setHumanInvolvement(chat_id, false)
        }

        return
      }

      if (await ChatDB.isHumanInvolvement(chat_id)) { // 人工参与，则不进行回复
        logger.log(chat_id, '联系人交给人工处理')
        // 存储下用户消息
        await ChatHistoryService.addUserMessage(chat_id, userMessage)
        return
      }

      // 0 点到 10 点之间的消息，延迟处理
      const isLateNight = new Date().getHours() < 10
      if (Config.setting.onlyReceiveMessage || isLateNight) { // mode
        console.log(chalk.redBright('0点 到 10点之间的消息不进行回复'))
        await ChatHistoryService.addUserMessage(chat_id, userMessage)
        await createLateNightTask(chat_id)
        return
      }

      // 下面开始处理消息
      if (userMessage.length >= 100) { // 用户发送的消息比较长的话，mock 一下人工阅读的时间
        await sleep(10 * 1000) // 延缓输出
      }

      await this.handleChatMessage(chat_id, senderId, userMessage)
    } catch (e) {
      if (!(e instanceof InterruptError)) {
        try {
          console.trace(e)
          logger.error('消息回复失败', e)
          await HumanTransfer.transfer(chat_id, senderId, HumanTransferType.MessageSendFailed)
        } catch (e) {
          logger.error('转人工失败', e)
        }
      }
    } finally {
      try {
        await this.saveChat(chat_id, senderId) // 注意，出错了，客户也要创建
      } catch (e) {
        console.error(e)
        logger.error('创建用户失败', senderId, e)
      }
    }
  }

  public static async handleChatMessage(chat_id: string, senderId: string, userMessage: string) {
    try {
      // 初始化聊天状态
      await ChatStatStoreManager.initState(chat_id)

      // 获取聊天记录并判断是否为空
      const chatHistory = await ChatHistoryService.getChatHistory(chat_id)
      if (chatHistory.length === 0) {
        // 更新 AI on 标签
        HumanTransfer.updateTags(senderId, WecomTag.AIOn)

        // 延时更新标签
        delayFc({
          h: 1,
          fc: async () => {
            try {
              const chatState = ChatStateStore.get(chat_id).state
              if (chatState !== ConversationState.End) {
                await XbbHelper.labelClue(chat_id, senderId)
              }
            } catch (e) {
              logger.error('延时更新标签失败')
            }
          }
        })
      }

      // 进行流程中的一步操作
      await BaoshuFlow.step(chat_id, senderId, userMessage)

      // 检查用户消息数量，如果是第一条，更新用户消息数统计
      const userMsgCount = await ChatHistoryService.getUserMessageCount(chat_id)
      if (userMsgCount === 1) {
        await MessageReplyService.countUserMsgGreaterThanOne(chat_id)
      }
    } catch (e) {
      if (!(e instanceof InterruptError)) {
        try {
          console.trace(e)
          logger.error('消息回复失败', e)
          await HumanTransfer.transfer(chat_id, senderId, HumanTransferType.MessageSendFailed)
        } catch (e) {
          logger.error('转人工失败', e)
        }
      }
    } finally {
      // 最终处理逻辑，保存聊天记录
      try {
        if (!(await isPastUser(senderId))) {
          await this.saveChat(chat_id, senderId) // 无论是否出错，都创建用户记录
        }
      } catch (e) {
        console.error(e)
        logger.error('创建用户失败', senderId, e)
      }
    }
  }


  /**
   * 存储用户消息大于 1 的个数
   * @param chat_id
   */
  public static async countUserMsgGreaterThanOne(chat_id: string) {
    try {
      const userCount = await PrismaMongoClient.getInstance().user_count.findUnique({ where: { id: chat_id } })
      if(!userCount) {
        await PrismaMongoClient.getInstance().user_count.create({
          data: {
            id: chat_id,
            count: 1,
            created_at: new Date()
          }
        })
      }
    } catch (e) {
      logger.error('Chat ID重复:', e) // 日志记录错误信息
    }
  }

  public static async saveChat(chat_id: string, senderId: string) {
    if (senderId === Config.setting.wechatConfig?.id) { // 不创建自己账号的信息
      return
    }

    if (!ChatStateStore.hasState(chat_id)) { // 如果空状态，数据库有数据，要进行恢复，不能直接覆盖
      await ChatStatStoreManager.initState(chat_id)
    }

    if (await ChatDB.getById(chat_id) === null) {
      const currentSender = await JuziAPI.getCustomerInfo(Config.setting.wechatConfig?.id as string, senderId)

      await ChatDB.create({
        id: chat_id,
        round_ids: [],
        contact: {
          wx_id: senderId,
          wx_name: currentSender ? currentSender.name : senderId,
        },
        wx_id: Config.setting.wechatConfig?.id as string,
        created_at: new Date(),
        chat_state: ChatStateStore.get(chat_id)
      })
    } else {
      // 更新下状态
      await ChatDB.updateState(chat_id, ChatStateStore.get(chat_id))
    }
  }
}


// 延迟消息处理
async function createLateNightTask(chat_id: string) {
  // 特别注意消息队列，尽量绑定到当前账号服务上消费，所以一定要绑定上账号 id, 如果不绑定会导致到其他账号消费可能会有未知问题
  const queue = new Queue(`lateNightCustomers_${Config.setting.wechatConfig?.id as string}`, {
    connection: RedisDB.getInstance(),
  })

  // 算一下跟 10 am 之间的时间差
  const currentTime = new Date()
  const tenAM = new Date()

  tenAM.setHours(10, (new Date().getMinutes() % 10), new Date().getSeconds(), 0)

  const timeDifference = tenAM.getTime() - currentTime.getTime()

  if (timeDifference < 0) {
    return
  }
  logger.trace(`凌晨信息延迟 ${timeDifference / 60 / 60 / 1000} h 处理`)

  await queue.add('lateNightDelayReply', { chat_id }, { jobId: `${chat_id  }_${ DateHelper.getFormattedDate(currentTime)}`, delay:  timeDifference }) // 使用 jobId 保证 同一个 chat_id 下的任务只被添加一次
}

// 启动队列监听
export function startWorker() {
  GlobalMessageHandlerService.startWorker()

  const worker = new Worker(`lateNightCustomers_${Config.setting.wechatConfig?.id as string}`, async (job) => {
    // 获取聊天记录，进行回复
    const chatHistory = await ChatHistoryService.getLastUserMessages(job.data.chat_id)
    if (chatHistory.length === 0) {
      return
    }

    // // 删除最后一组聊天记录
    await ChatHistoryService.deleteByIds(chatHistory.map((message) => message.id))

    // 将最后一组，未回复的 用户信息进行回放
    // 把这些聊天记录删除
    const userMessage = chatHistory.map((message) => message.content).join('\n')
    const chat_id = job.data.chat_id
    const user_id = getUserId(chat_id)

    await MessageReplyService.handleChatMessage(chat_id, user_id, userMessage)
  }, {
    connection: RedisDB.getInstance(),
    concurrency: 2,
    lockDuration: 90 * 10000, // 设置超时时间为 90 秒（以毫秒为单位）
  })

  // 监听任务失败事件
  worker.on('error', (err) => {
    logger.error(err)
  })
}
