#!/usr/bin/env ts-node
/**
 * 部署打包脚本 for the bot project
 *
 * 支持：
 *  - 通过 inquirer 输入版本号，如果输入为空则自动生成当前日期时间作为版本号
 *  - 交互式多选服务进行部署（使用 inquirer 的 checkbox）
 */

import * as fs from 'fs'
import { execSync } from 'child_process'
import inquirer from 'inquirer'
import chalk from 'chalk'
import yaml from 'js-yaml'
import path from 'path'

// Define the type for the parsed docker-compose structure
interface DockerCompose {
    services: Record<string, any>
}

// ===== 配置变量 =====
const IMAGE_NAME = 'baoshu_bot_image'
const REMOTE_REPO = 'crpi-5ht6itmmkqtumodb.cn-hangzhou.personal.cr.aliyuncs.com/freespirit/baoshu'
const LOG_FILE = path.join(__dirname, 'deploy_script.log')
const DOCKER_COMPOSE_FILE = path.join(__dirname, 'docker-compose.yaml')
const PROJECT_ROOT = process.cwd()

// 读取可用服务
const doc: DockerCompose = yaml.load(fs.readFileSync(DOCKER_COMPOSE_FILE, 'utf8')) as DockerCompose
const availableServices: string[] = Object.keys(doc.services)

// ===== 工具函数 =====
/**
 * 获取默认版本号：格式为 YYYY-MM-DD.HH-MM-SS
 */
function getDefaultVersion(): string {
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const day = String(now.getDate()).padStart(2, '0')
  const hours = String(now.getHours()).padStart(2, '0')
  const minutes = String(now.getMinutes()).padStart(2, '0')
  const seconds = String(now.getSeconds()).padStart(2, '0')
  return `${year}-${month}-${day}.${hours}-${minutes}-${seconds}`
}

/**
 * 日志函数，将日志同时输出到控制台和日志文件中
 * @param message 要记录的日志信息
 * @param color
 */
function log(message: string, color: keyof typeof chalk = 'white') {
  const timestamp = new Date().toISOString().replace('T', ' ').substring(0, 19)
  const coloredMessage = (chalk[color] as (text: string) => string)(`[${timestamp}] ${message}`)
  const plainMessage = `[${timestamp}] ${message}`
  fs.appendFileSync(LOG_FILE, `${plainMessage}\n`)
  console.log(coloredMessage)
}

/**
 * 执行 shell 命令，若失败则退出
 * @param command 命令字符串
 */
function runCommand(command: string) {
  try {
    // Run commands from the project root directory
    execSync(command, { stdio: 'inherit', cwd: PROJECT_ROOT })
  } catch (error) {
    log(`命令执行失败: ${command}`, 'red')
    process.exit(1)
  }
}

// ===== 主流程 =====
async function main() {
  log('===== 开始部署流程 (bot) =====', 'cyan')

  // 1. 获取版本号
  const { versionInput } = await inquirer.prompt([
    {
      type: 'input',
      name: 'versionInput',
      message: '请输入版本号（留空自动生成）：',
      default: getDefaultVersion(),
    },
  ])
  const VERSION = versionInput.trim() || getDefaultVersion()
  log(`使用的版本号: ${VERSION}`, 'yellow')

  // 2. 选择要部署的服务
  const { services } = await inquirer.prompt([
    {
      type: 'checkbox',
      name: 'services',
      message: '请选择要部署的服务 (空格键选择，回车确认):',
      choices: availableServices,
      validate(answer: string[]) {
        if (answer.length < 1) {
          return '请至少选择一个服务进行部署！'
        }
        return true
      },
    },
  ])
  const selectedServices: string[] = services
  log(`选中的服务: ${selectedServices.join(', ')}`, 'green')

  // 3. 构建、标记和推送镜像
  log('===== 本地构建与推送流程 =====', 'cyan')
  const localImageTag = `${IMAGE_NAME}:${VERSION}`
  log(`构建 Docker 镜像: ${localImageTag}`, 'blue')

  // The Dockerfile is in the 'docker' subdirectory. The context should be the project root.
  // The command is run from PROJECT_ROOT, so paths are relative to it.
  runCommand(`docker buildx build --platform linux/amd64 -f ./Dockerfile -t ${localImageTag} .`)

  log(`标记镜像: ${localImageTag} -> ${REMOTE_REPO}:${VERSION}`, 'magenta')
  runCommand(`docker tag ${localImageTag} ${REMOTE_REPO}:${VERSION}`)

  log(`标记镜像: ${localImageTag} -> ${REMOTE_REPO}:latest`, 'magenta')
  runCommand(`docker tag ${localImageTag} ${REMOTE_REPO}:latest`)

  log('推送镜像到远程仓库...', 'blue')
  runCommand(`docker push ${REMOTE_REPO}:${VERSION}`)
  runCommand(`docker push ${REMOTE_REPO}:latest`)
  log('镜像推送完成', 'green')

  // 4. 执行远程部署
  log('===== 服务器部署流程 =====', 'cyan')
  // NOTE: Ensure this path is correct on your server.
  const remoteProjectDir = '/root/wechaty_bot'
  const sshCommand = `ssh root@139.224.228.125 "cd ${remoteProjectDir} && git pull && cd bot/docker && ./server_deploy.sh ${selectedServices.join(' ')}"`

  log(`执行远程部署命令: ${sshCommand}`, 'yellow')
  runCommand(sshCommand)

  log(`部署流程完成，版本号: ${VERSION}`, 'cyan')
  // Optional: Open a monitoring URL after deployment
  // exec('open "YOUR_MONITORING_URL_HERE"');
}

// 执行主流程
main().catch((error) => {
  log(`发生未捕获的错误: ${error.message}`, 'red')
  process.exit(1)
})