import { Client } from '@elastic/elasticsearch'
import { Config } from '../../config/config'
import { Retry } from '../../lib/retry/retry'
import { AzureOpenAIEmbedding } from '../../lib/ai/llm/openai_embedding'

export interface IElasticEmbeddingRes {
  pageContent: string
  metadata: Record<string, any>
  score: number
}

class ElasticSearchClient {
  private static client: Client | undefined
  public static getInstance() {
    if (!this.client) {
      this.client = new Client ({
        node: Config.setting.elasticSearch.url,
        auth: {
          username: Config.setting.elasticSearch.username,
          password: Config.setting.elasticSearch.password
        }
      })
    }
    return this.client
  }
}


class ElasticSearchService {

  static async deleteIndex(indexName: string) {
    const client = ElasticSearchClient.getInstance()
    const indexExists = await client.indices.exists({ index: indexName })
    if (!indexExists) {
      return
    }

    await client.indices.delete({
      index: indexName
    })
  }

  static async createIndex (index: string, mappings: object) {
    try {
      const response = await ElasticSearchClient.getInstance().indices.create ({
        index,
        body: { mappings }
      })
      console.log ('Index created:', index)
      return response
    } catch (error) {
      console.error ('Error creating index:', error)
      throw error
    }
  }

  static async insertDocuments(index: string, documents: object[]) {
    try {
      const body = documents.flatMap((doc) => [{ index: { _index: index } }, doc])

      const response = await ElasticSearchClient.getInstance().bulk({ refresh: true, body })

      console.log('Documents indexed')
      return response
    } catch (error) {
      console.error('Error indexing documents:', error)
      throw error
    }
  }

  static async insertDocument (index: string, body: object) {
    try {
      const response = await ElasticSearchClient.getInstance().index ({
        index,
        body,
        refresh: true
      })

      console.log ('Document indexed')
      return response
    } catch (error) {
      console.error ('Error indexing document:', error)
      throw error
    }
  }

  static async search (index: string, query: object, size = 10, options?: object): Promise<any[]> {
    try {
      const response = await Retry.retry(3, async () => {
        return ElasticSearchClient.getInstance().search({
          index,
          body: { query, ...options },
          size: size
        })
      },  { delayFunc: (count) => count * 1000 })

      if (response.hits.hits) {
        return response.hits.hits
      }
      return []
    } catch (error) {
      console.error ('Error searching documents:', error)
      throw error
    }
  }

  static async count (index: string): Promise<number> {
    try {
      const response = await ElasticSearchClient.getInstance().count({
        index
      })

      if (response.count) {
        console.log ('Count:', response.count)
        return response.count
      }
      return 0
    } catch (error) {
      console.error ('Error counting documents:', error)
      throw error
    }
  }

  public static async embeddingSearch(
    index: string,
    query: string,
    k = 10,
    lowestScore: number = 0.6,
    filter?: object
  ): Promise<IElasticEmbeddingRes[]> {
    const embeddings = await AzureOpenAIEmbedding.getInstance().embedQuery(query)

    const searchBody = {
      knn: {
        field: 'embedding',
        query_vector: embeddings,
        num_candidates: 50,
        k: k,
        filter: filter
      }
    }

    const res = await ElasticSearchClient.getInstance().search({
      index: index,
      body: searchBody,
    })

    return res.hits.hits
      .map((hit: any): IElasticEmbeddingRes => ({
        pageContent: hit._source.text,
        metadata: hit._source.metadata,
        score: hit._score
      }))
      .filter((result) => result.score > lowestScore)
  }

  static async describeIndex(index: string) {
    try {
      const response = await ElasticSearchClient.getInstance().indices.getMapping({
        index: index
      })
      console.log('Index description:', JSON.stringify(response, null, 4))
      return response
    } catch (error) {
      console.error('Error describing index:', error)
      throw error
    }
  }
}

export default ElasticSearchService