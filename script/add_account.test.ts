import { JuziAPI } from '../bot/lib/juzi/api'
import { PrismaMongoClient } from '../bot/model/mongodb/prisma'
import axios from 'axios'

describe('Test', function () {
  beforeAll(() => {

  })

  it('1234', async () => {
    // 获取对应的群
    const groupIdMap = {
      '1': 'R:*****************',
      '2': 'R:*****************',
      '3': 'R:*****************',
      '4': 'R:*****************'
    }

    // 拉一下 config, 根据 ID 做下去重
    const configs = await PrismaMongoClient.getConfigInstance().config.findMany(
      {
        where: {
          enterpriseName: 'baoshu'
        }
      }
    )


  }, 30000)

  it('获取账号信息', async () => {
    // 获取微信
    const res = await JuziAPI.listAccounts('661cfbe507ea8c7bacfe5c8c')
    const accounts = res.data

    // 拉一下 config, 根据 ID 做下去重
    const configs = await PrismaMongoClient.getConfigInstance().config.findMany(
      {
        where: {
          enterpriseName: 'baoshu'
        }
      }
    )

    const toAddAccounts: any[] = []

    for (const account of accounts) {
      const config = configs.find((config) => config.wechatId === account.wxid)

      if (config) {
        toAddAccounts.push(config.accountName)
      }
    }

    console.log(JSON.stringify(toAddAccounts, null, 4))
  }, 30000)

  it('', async () => {
    const res = await JuziAPI.listAccounts('661cfbe507ea8c7bacfe5c8c')
    const accounts = res.data

    console.log(JSON.stringify(accounts, null, 4))

    const group1: any[] = []
    const group2: any[] = []
    const group3: any[] = []
    const group4: any[] = []

    const groupIdMap = {
      '1': 'R:*****************',
      '2': 'R:*****************',
      '3': 'R:*****************',
      '4': 'R:*****************'
    }

    for (const account of accounts) {
      if (account.nickName.includes('顾问一组') || account.nickName.includes('顾问四组')) {
        group1.push({ name: account.nickName, wechatId: account.wxid, groupId: groupIdMap['1'] })
        await PrismaMongoClient.getConfigInstance().config.update({
          where: {
            wechatId: account.wxid
          },
          data: {
            enterpriseConfig: {
              notifyGroupId: groupIdMap['1'],
              counselors: ['HuRong_1'],
              xbbId: 'woOGQmBgAAi3qA-7gxhr3zPGLjQw_pEw4'
            }
          }
        })
      } else if (account.nickName.includes('顾问二组')) {
        group2.push({ name: account.nickName, wechatId: account.wxid, groupId: groupIdMap['2'] })

        await PrismaMongoClient.getConfigInstance().config.update({
          where: {
            wechatId: account.wxid
          },
          data: {
            enterpriseConfig: {
              notifyGroupId: groupIdMap['2'],
              counselors: ['GaoTianJiao'],
              xbbId: 'woOGQmBgAACVccobLIbnVHNYabi5A12Q'
            }
          }
        })
      } else if (account.nickName.includes('顾问三组')) {
        group3.push({ name: account.nickName, wechatId: account.wxid, groupId: groupIdMap['3'] })

        await PrismaMongoClient.getConfigInstance().config.update({
          where: {
            wechatId: account.wxid
          },
          data: {
            enterpriseConfig: {
              notifyGroupId: groupIdMap['3'],
              counselors: [],
              xbbId: ''
            }
          }
        })
      } else if (account.nickName.includes('低龄助理老师')) {
        group4.push({ name: account.nickName, wechatId: account.wxid, groupId: groupIdMap['4'] })

        await PrismaMongoClient.getConfigInstance().config.update({
          where: {
            wechatId: account.wxid
          },
          data: {
            enterpriseConfig: {
              notifyGroupId: groupIdMap['4'],
              counselors: [],
              xbbId: ''
            }
          }
        })
      }
    }

    console.log(JSON.stringify(group1, null, 4))
    console.log(JSON.stringify(group2, null, 4))
    console.log(JSON.stringify(group3, null, 4))
    console.log(JSON.stringify(group4, null, 4))

  }, 30000)

  it('should pass', async () => {
    // 获取微信
    const res = await JuziAPI.listAccounts('661cfbe507ea8c7bacfe5c8c')
    const accounts = res.data

    // 拉一下 config, 根据 ID 做下去重
    const configs = await PrismaMongoClient.getConfigInstance().config.findMany(
      {
        where: {
          enterpriseName: 'baoshu'
        }
      }
    )

    const toAddAccounts: any[] = []

    for (const account of accounts) {
      const config = configs.find((config) => config.wechatId === account.wxid)
      if (account.weixin !== 'pyz3')
        continue

      if (!config) {
        toAddAccounts.push(account)
      }
    }

    // 添加暴叔账号
    // 从 最后一个添加的 baoshu 开始 +1
    const lastIndex = Number(configs.sort((a, b) => Number(a.accountName.replace('baoshu', '')) - Number(b.accountName.replace('baoshu', '')))[configs.length - 1].accountName.replace('baoshu', ''))

    let index = lastIndex + 1

    // 安装顺序创建账户
    for (const toAddAccount of toAddAccounts) {
      await  PrismaMongoClient.getConfigInstance().config.create({
        data: {
          'enterpriseName': 'baoshu',
          'accountName': `baoshu${index}`,
          'wechatId': toAddAccount.wxid,
          'address': `http://*************:${index + 3004}`,
          'port': `${index + 3004}`,
          'botUserId': toAddAccount.weixin,
          'orgToken': '661cfbe507ea8c7bacfe5c8c',
          'enterpriseConfig': {
            'notifyGroupId': 'R:*****************',
            'counselors': [],
            'xbbId': ''
          }
        }
      })

      index++
    }

    let i = lastIndex + 1

    // log docker 配置
    for (const toAddAccount of toAddAccounts) {
      console.log(`
  baoshu${i}:
    image: crpi-5ht6itmmkqtumodb.cn-hangzhou.personal.cr.aliyuncs.com/freespirit/baoshu:latest
    container_name: baoshu${i}
    environment:
      - WECHAT_NAME=baoshu${i}
      - TZ=Asia/Shanghai
    ports:
      - "${3004 + i}:${3004 + i}"
    mem_limit: 2g
    restart: always
    command: ["npm", "run", "start:client"]`)

      i++
    }

    console.log('code bot/docker/docker-compose.yaml')

    const clearRes = await axios.post('http://*************:6001/api/clear-server-address-cache')
    console.log(JSON.stringify(clearRes.data, null, 4))
  })

  it('清除缓存', async () => {
    const clearRes = await axios.post('http://*************:6001/api/clear-server-address-cache', {}, {
      headers: {
        'Content-Type': 'application/json'
      }
    })
    console.log(JSON.stringify(clearRes.data, null, 4))
  }, 30000)

  it('jijiji', async () => {
    await PrismaMongoClient.getConfigInstance().config.create({
      data: {
        'enterpriseName': 'baoshu',
        'accountName': 'baoshu$19',
        'wechatId': '****************',
        'address': 'http://*************:3023',
        'port': '3023',
        'botUserId': 'BaoShu01',
        'orgToken': '661cfbe507ea8c7bacfe5c8c',
        'enterpriseConfig': {
          'notifyGroupId': 'R:*****************',
          'counselors': [],
          'xbbId': ''
        }
      }
    })
  })

  it('123123', async () => {
    console.log(await JuziAPI.listAccounts('661cfbe507ea8c7bacfe5c8c'))
  }, 30000)
})