{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["1:1 - Cannot find name 'AoChuangWechatContact'.\n", "1:40 - <PERSON><PERSON> find name 'CacheDecorator'.\n", "1:69 - Cannot find name 'AoChuangWechatContact'.\n", "4:23 - <PERSON><PERSON> find name 'readLog<PERSON><PERSON>'.\n", "4:36 - Cannot find name 'path'.\n", "11:28 - <PERSON><PERSON> find name 'getU<PERSON><PERSON><PERSON>'.\n"]}], "source": ["import path from \"path\";\n", "import {AoChuangWechatContact} from \"../bot/lib/auchuang/openapi/contact\";\n", "import {CacheDecorator} from \"../bot/lib/cache/cache\";\n", "import {JSONHelper} from \"../bot/lib/json/json\";\n", "import {init<PERSON><PERSON><PERSON>, ZiliaoExecutor} from \"../bot/service/diantou/components/user_trust_simulator/ziliao\";\n", "import {Config} from \"../bot/config/config\";\n", "\n", "\n", "AoChuangWechatContact.getContactById = CacheDecorator.decorateAsync(AoChuangWechatContact.getContactById)\n", "// 数据分析\n", "// 聚合不同事件下的错误\n", "const rawData = await readLogFiles(path.join(__dirname, '2024-03-25'))\n", "// 替换 chat_id 为人名\n", "// 按照事件分类\n", "const eventMap = new Map<string, any[]>()\n", "\n", "for (let data of rawData) {\n", "  try {\n", "    data.user_name = await getUserName(data.chat_id)\n", "  } catch (e) {\n", "    console.log(e)\n", "  }\n", "  if (!data.user_name) {\n", "    continue\n", "  }\n", "\n", "  if (data.user_name.includes('麦子') || data.user_name.includes('SYQ') || data.user_name.includes('鸣'))\n", "    continue\n", "\n", "  if (!eventMap.has(data.user_name)) {\n", "    eventMap.set(data.user_name, [])\n", "  }\n", "  eventMap.get(data.user_name)?.push(data.event)\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "TypeScript", "language": "typescript", "name": "tslab"}, "language_info": {"codemirror_mode": {"mode": "typescript", "name": "javascript", "typescript": true}, "file_extension": ".ts", "mimetype": "text/typescript", "name": "typescript", "version": "3.7.2"}}, "nbformat": 4, "nbformat_minor": 2}