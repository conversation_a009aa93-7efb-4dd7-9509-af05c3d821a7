// 根据环境变量初始化配置
import chalk from 'chalk'
import { Config } from '../../bot/config/config'
import { WechatMessageSender } from '../../bot/service/baoshu/components/message_send'
import { sleep } from '../../bot/lib/schedule/schedule'
import { ChatDB } from '../../bot/service/baoshu/database/chat'
import { SilentReAsk } from '../../bot/service/baoshu/components/silent_requestion'
import { AoChuangWechatContact } from '../../bot/lib/auchuang/openapi/contact'
import { ChatHistoryService } from '../../bot/service/baoshu/components/chat_history'
import { EventTracker } from '../../bot/model/logger/data_driven'
import { IMessageType } from '../../bot/service/message/message'
import { Account, ClientAccountConfig } from '../config/config'
import { ChatStateStore, ChatStatStoreManager } from '../../bot/service/baoshu/storage/chat_state_store'
import { WechatAccountDB } from '../../bot/service/baoshu/database/user'
import logger from '../../bot/model/logger/logger'
import { startWorker } from '../../bot/service/message/message_reply'

async function InsertAccountInfo(account: Account) {
  if (!await WechatAccountDB.getById(account.wechatId)) {
    await WechatAccountDB.create({
      id: account.wechatId,
      name: account.nickname,
      avatar: null
    })

    logger.trace('新建账号：', account.nickname)
  }
}

export async function initConfig() {
  // 环境配置
  const env = process.env.NODE_ENV ?? 'dev'
  if (!env) {
    console.error('请设置环境变量 NODE_ENV')
    process.exit(1)
  }

  Config.setting.startTime = Date.now()
  Config.setting.BOT_NAME = '暴叔'

  // 注入配置
  // 读取注入的 姓名
  const name = process.env.WECHAT_NAME
  if (!name) {
    console.error('请设置环境变量 WECHAT_NAME')
    process.exit(1)
  }

  const account = await ClientAccountConfig.getAccountByName(name)
  if (!account) {
    console.error(`找不到${name}对应的账号`)
    process.exit(1)
  }

  Config.setting.wechatConfig = {
    id: account.wechatId,
    name: account.nickname,
    orgToken: account.orgToken,
    botUserId: account.botUserId,
    notifyGroupId: account.notifyGroupId,
    counselorIds: account.counselors,
    xbbId: account.xbbId
  }
  console.log(chalk.green(`当前账号：${account.nickname}(${account.wechatId})`))
  // mockAddFriend()

  if (Config.setting.onlyReceiveMessage) { // mode
    console.log(chalk.redBright('开启仅接收消息模式，请注意此模式下只接受消息，不会进行AI回复'))
  }

  // 数据库插入账号信息
  await InsertAccountInfo(account)
  startWorker()
}