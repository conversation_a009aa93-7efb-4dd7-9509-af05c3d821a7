import axios from 'axios'
import { Retry } from '../../bot/lib/retry/retry'
import { ClientAccountConfig } from '../config/config'
import { z } from 'zod'
import { ISendMessageResult } from '../../bot/lib/juzi/type'


export class EventDispatcher {
  private static async getServerAddress(wechatId: string) {
    const serverAddress = await ClientAccountConfig.getServerAddressByWechatId(wechatId)
    if (!serverAddress) {
      throw new Error(`没有找到对应的服务器地址 ${wechatId}`)
    }

    return serverAddress
  }

  private static async dispatchEventToServer(serverAddress: string, event: any, options?: {
    isMsg?: boolean
    isAoChuang?: boolean
    isSendResult?: boolean
  }) {
    if (!serverAddress) {
      console.error('没有找到对应的服务器地址')
      return
    }

    try {
      await Retry.retry(3, async () => {
        let subRoute = 'event'
        if (options?.isMsg) {
          subRoute = 'message'
        }

        if (options?.isAoChuang) {
          subRoute = 'aochuang/event'
        }

        if (options?.isSendResult) {
          subRoute = 'sendResult'
        }


        await axios.post(`${serverAddress}/${subRoute}`, event, { insecureHTTPParser: true, timeout: 5 * 1000 })
      }, {
        delayFunc: (retryCount) => {
          if (retryCount === 1) return 5 * 1000
          if (retryCount === 2) return 15 * 1000
          if (retryCount === 3) return 30 * 1000
          return 0  // 之后不再进行重试
        }
      })
    } catch (e) {
      console.error('事件分发失败：', serverAddress, e)
    }
  }

  static async dispatchMessage(data: any) {
    console.log(JSON.stringify(data, null, 4))

    const serverAddress = await this.getServerAddress(data.imBotId)

    await this.dispatchEventToServer(serverAddress, data, { isMsg: true })
  }

  static async dispatchEvent(data: any, isMsg?: boolean) {
    console.log(JSON.stringify(data, null, 4))

    // 掉线通知， https://s.apifox.cn/71b1c1e2-7473-4473-9e25-d2091b22199e/api-*********
    if (data.eventType && data.eventType === 4 && data.logoutReason && data.bot) {
      if (data.bot.group  && data.bot.group.groupName && data.bot.group.groupName.includes('宇合')) { // 宇合通知
        await axios.get(`https://fwalert.com/2ccf5965-d33a-4562-96d6-2ca9ba0a652c?account=${encodeURIComponent(this.numberToChinese(data.bot.name))}&reason=${encodeURIComponent(data.logoutReason)}`)
      } else {
        await axios.get(`https://fwalert.com/32622b0f-9455-43ea-8f0d-cc3ece5a4ab2?account=${encodeURIComponent(this.numberToChinese(data.bot.name))}&reason=${encodeURIComponent(data.logoutReason)}`)
      }

      return
    }

    if (data.botInfo) {
      const serverAddress = await this.getServerAddress(data.botInfo.imBotId)

      await this.dispatchEventToServer(serverAddress, data, { isMsg })
    }
  }

  static async dispatchAoChuangEvent(event: any) {
    const eventData = event.data as any[]

    if (Array.isArray(eventData) && eventData.every(this.isAoChuangMsg)) {
      for (const eventDatum of eventData) {
        await this.dispatchAoChuangMessage(eventDatum)
      }
    } else {
      console.log(JSON.stringify(event, null, 4), 'unHandled AoChuang event')
    }
  }

  public static isAoChuangMsg(eventData: any) {
    const schema = z.object({
      id: z.string(),
      messageId: z.string(),
      userName: z.string(),
      whatsId: z.string(),
      friendWhatsId: z.string(),
      currentWhatsId: z.string(),
      actionType: z.number(),
      chatType: z.number(),
      messageStatus: z.number(),
      content: z.string(),
      contentType: z.number(),
      sendTime: z.string(),
      originType: z.number(),
      userProfile:  z.object({
        id: z.number(),
        name: z.string(),
        whatsApp: z.string(),
        username: z.string(),
        extendsMap: z.string(),
        income: z.string(),
        profession: z.string(),
        desc: z.string(),
        tags: z.array(z.string()),
        email: z.string(),
        address: z.string(),
      })
    })


    const result = schema.safeParse(eventData)

    return result.success
  }

  private static async dispatchAoChuangMessage(eventDatum: any) {
    const serverAddress = await this.getServerAddress(eventDatum.currentWhatsId)

    await this.dispatchEventToServer(serverAddress, eventDatum, { isAoChuang: true })
  }


  private static numberToChinese(text: string): string {
    // 阿拉伯数字到中文数字的映射
    const chineseNumbers = {
      '0': '零',
      '1': '一',
      '2': '二',
      '3': '三',
      '4': '四',
      '5': '五',
      '6': '六',
      '7': '七',
      '8': '八',
      '9': '九'
    }

    // 替换文本中的每个数字为对应的中文数字
    return text.replace(/\d+/g, (match) => {
      if (match.length === 2 && match[0] === '1') {
        // 如果是两位数并且第一位是 1，则将其替换为 '十'
        return `十${  chineseNumbers[match[1]]}`
      }
      return match.split('').map((digit) => chineseNumbers[digit]).join('')
    })
  }

  static async dispatchSendResult(data: ISendMessageResult) {
    // 对发送成功的结果进行处理
    const serverAddress = await ClientAccountConfig.getServerAddressByWechatId(data.imBotId)
    if (!serverAddress) {
      return
    }

    await this.dispatchEventToServer(serverAddress, data, { isSendResult: true })
  }
}