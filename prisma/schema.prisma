generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mongodb"
  url      = "mongodb://root:free1234$spirit!@dds-bp100ccf7c2e7f741791-pub.mongodb.rds.aliyuncs.com:3717/baoshu?authSource=admin"
}

type ChatContact {
  wx_id   String
  wx_name String
}

type ConversationRoundLogChatHistory {
  id         String?
  chat_id    String?
  content    String
  role       String
  created_at DateTime? @db.Date
}

type ChatState {
  slotAskedCount      Json
  nodeInvokeCount     Json
  nextStage           String
  projects            Project[]
  pickedMiddleProject String?
  userSlots           Json?
  intentions          String[]
  state               String
  counselor_id        String?
  status              Json?
}

type CommonSlots {
  is_study_abroad   Boolean?
  current_education String?
  is_parent         Boolean?
  goal              String?
  budget            Budget?
}

type Budget {
  quantity    String?
  is_per_year Boolean?
}

type Project {
  educationStage         String
  isStudyAbroad          String
  budgetLowerBound       String
  budgetUpperBound       String
  annualBudgetLowerBound String
  annualBudgetUpperBound String
  projectName            String
  projectDescription     String
  projectAdvantages      String
  userStory              String
  academicYears          String
  isJapaneseStudent      Boolean?
}

type ConversationRoundLogStages {
  input        String
  name         String
  output       String
  chat_history ConversationRoundLogChatHistory[]
}

model chat {
  id                String      @id @map("_id")
  contact           ChatContact
  round_ids         String[]
  wx_id             String
  is_human_involved Boolean?
  created_at        DateTime?   @db.Date
  chat_state        ChatState
}

model chat_history {
  id         String   @id @default(auto()) @map("_id") @db.ObjectId
  chat_id    String
  content    String
  created_at DateTime @db.Date
  role       String

  @@index([chat_id, created_at], map: "chat_id_1_created_at_1")
}

model account {
  id     String  @id @map("_id")
  name   String
  avatar String?
}

model group {
  id        String  @id @map("_id")
  name      String
  isCurrent Boolean @default(false)
}

model new_friend {
  id       String @id @map("_id")
  friendId String
  name     String
  wechatId String
}

model log_store {
  id        String   @id @default(auto()) @map("_id") @db.ObjectId
  chat_id   String?
  level     String
  timestamp DateTime @db.Date
  msg       String
}

model user_count {
  id         String   @id @map("_id")
  count      Int
  created_at DateTime @db.Date
}

model config {
  id               String @id @default(auto()) @map("_id") @db.ObjectId
  enterpriseName   String
  accountName      String
  wechatId         String @unique
  address          String
  port             String
  botUserId        String
  orgToken         String
  enterpriseConfig Json

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}
