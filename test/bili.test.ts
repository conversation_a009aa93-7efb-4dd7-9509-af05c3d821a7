import { UserEducationGroup } from '../bot/service/baoshu/components/flow/nodes/intentionCalculate'
import { ChatStateStore } from '../bot/service/baoshu/storage/chat_state_store'
import axios from 'axios'
import { LLM } from '../bot/lib/ai/llm/LLM'
import { PrismaMongoClient } from '../bot/model/mongodb/prisma'
import { ChatHistoryService } from '../bot/service/baoshu/components/chat_history'
import { ClientAccountConfig } from '../bot_starter/config/config'
import { JuziAPI } from '../bot/lib/juzi/api'
import { Queue } from 'bullmq'
import { RedisCacheDB } from '../bot/model/redis/redis_cache'


describe('Test', function () {
  beforeAll(() => {

  })

  it('查看队列 worker', async () => {
    const queue = new Queue('user-message-****************', {
      connection: RedisCacheDB.getInstance()
    })

    await console.log(JSON.stringify(await queue.getWorkers(), null, 4))
  }, 30000)

  it('************', async () => {
    console.log(JSON.stringify(await ChatHistoryService.getChatHistory('7881301047907394_1688855837567386'), null, 4))
  }, 30000)

  it('************1231233', async () => {
    const configs = await PrismaMongoClient.getConfigInstance().config.findMany()
    console.log(JSON.stringify(configs, null, 4))
  }, 30000)

  it('123123', async () => {
    const a = 28
    const b = 3

    if((a ?? 0 > 30) && b >= 3) {
      console.log('hi')
    }
  }, 30000)

  it('213123123', async () => {
    console.log(new Date(1756888959259).toLocaleString())
  }, 30000)

  it('externalId', async () => {
    const customerInfo = await JuziAPI.getCustomerInfo('1688857859705686', '7881300846030208')

    if (customerInfo) {
      console.log(customerInfo.imInfo.externalUserId)
    }

    console.log(await JuziAPI.wxIdToExternalUserId('7881300846030208'))
  }, 30000)

  it('12312890', async () => {
    await JuziAPI.createRoom({
      botUserId: 'ZhaiShuai01',
      userIds: ['WangZiYang'],
      name: 'AI 拉群测试',
      greeting: '咱们在这里',
      externalUserIds: ['wmagdSKgAAwax00OkOQH81zvMYndO5sQ']
    })
  }, 30000)

  it('***********', async () => {
    console.log(new Date('2025-06-02T12:00:00+08:00').toLocaleString())
  }, 60000)

  it('clear cache', async () => {
    const res = await axios.post('http://localhost:6001/api/clear-server-address-cache')
    console.log(JSON.stringify(res.data, null, 4))

  }, 60000)

  it('address', async () => {
    const serverAddress = await ClientAccountConfig.getServerAddressByWechatId('************')
    console.log(serverAddress)
  }, 60000)

  it('numberToChinese', async () => {
    function numberToChinese(text: string): string {
      // 阿拉伯数字到中文数字的映射
      const chineseNumbers = {
        '0': '零',
        '1': '一',
        '2': '二',
        '3': '三',
        '4': '四',
        '5': '五',
        '6': '六',
        '7': '七',
        '8': '八',
        '9': '九'
      }

      // 替换文本中的每个数字为对应的中文数字
      return text.replace(/[0-9]/g, (match) => chineseNumbers[match])
    }

    console.log(numberToChinese('123我213操213你0妈4567890啊123'))
  }, 60000)

  it('12312', async () => {
    const now = new Date()
    const currentMonth = now.getMonth()
    const currentDate = now.getDate()
    const currentHour = now.getHours()
    console.log(currentDate, currentHour, currentMonth)
  }, 60000)

  it('聊天记录', async () => {
    await ChatHistoryService.clearChatHistory('7881301552229493_1688858091484181')
  }, 60000)

  it('add', async () => {
    await ChatHistoryService.addUserMessage('7881301552229493_1688858091484181', '那对于专业的选择和以后毕业的规划暴叔您有什么推荐呢\n我比较倾向于土壤方面和遥感方面，去哪所学校比较好呢')
  }, 60000)

  it('should pass', async () => {
    const url = 'www.bilibili.com/video/BV1234567890'

    // 定义用于匹配Bilibili视频链接的正则表达式
    const regex = /(?:https?:\/\/)?(?:www\.)?bilibili\.com\/video\/(?:av(\d+)\/)?(?:BV(\w+))/i
    // 尝试匹配给定的URL
    console.log(regex.test(url))
  })

  it('test', async () => {
    const userMessages =  [{ timestamp: 1234567890 }, { timestamp: 1 }]
    userMessages.sort((msgA, msgB) => {
      return msgA.timestamp - msgB.timestamp
    })

    console.log(userMessages)
  }, 30000)


  it('', async () => {
    const msg = '我是大二的学生'
    const welcomeMsgRegex = /^我是.{1,10}$/
    console.log(welcomeMsgRegex.test(msg))
  }, 60000)

  it('time compare', async () => {
    console.log(Number(new Date().getTime()) < new Date('2024-06-14T17:23:00+08:00').getTime())
  }, 60000)

  it('null', async () => {
    console.log(null as any instanceof Object)
  }, 60000)

  it('m', async () => {
    const supplementPrompts = new Map<UserEducationGroup, string>([
      [UserEducationGroup.JuniorTwoToFour, `以下是初中升学经常推荐的路径，以推荐的频次作为优先级排序，可以进行参考，但是注意推荐的项目一定要符合客户当前告知你的预算。预算内没有符合路径，就不推荐任何项目。**注意预算要严格按照以下提供的数字进行解答，这对我的工作非常重要**
1. 国内读国际高中
  - 特点：总预算80w，学习3年，对于高中不放心出国，又想未来读海外名校本科的首选。
  - 优势：提前适应国外课程体系，拓展人脉，不同的国际高中体系对应不同的国家升学，本科升学优势明显。

2. 国内读本科预科 + 新加坡本科课程
  - 特点：国内2年，新加坡两年，省时，省钱。总预算85w
  - 预算：85W搞定高中和本科。
  - 适用对象：对于不想孩子过早出国留学的家庭。

3. 出国读高中
  - 特点：提前适应海外生活，海外高中的学习经历和成绩，对于冲刺海外名校优势明显。新西兰高中学制3年总预算90w，加拿大高中3年总预算90w，美国高中4年总预算200w。
  - 适用对象：初中毕业直接到海外大学本科学位的首选。

4. 出国快速本科
  - 特点：海外大学本科直通车，省时，省钱。新加坡私立大学方向预计总预算100w，新西兰初升本方向150w
  - 优势：比国内同年龄人更早拿到本科文凭。`],
      [UserEducationGroup.HighSchool,  `以下是高中生升学经常推荐的路径，以推荐的频次作为优先级排序，可以进行参考，但是注意推荐的项目一定要符合客户当前告知你的预算。预算内没有符合路径，就不推荐任何项目。**注意预算要严格按照以下提供的数字进行解答，这对我的工作非常重要**
1. 国际本科3+1/2+2
  - 高考失利和无法上本科的同学的海外升学捷径，对于想体验留学生活的同学，意味着花更少的钱，本科保底，逆袭海外名校
  - 国家本科3+1，总预算50-80w，每年15w以上，分数在本科线下，英语单科在60以上，对接学校一般在QS200-300；2+2的总预算60-80w，成绩一般，英语单科80分上。不想在国内读普通本科的同学。
  
2. 港澳方向
  - 澳门总预算80w+，香港100w+；每年20-35w,“高考+留学”双保险。港澳申请与高考时间不冲突，可提前申请港澳学校，又可参加高考志愿填报，并且港澳离大陆近，对于留学不想去太远的家庭的首选。需要学生成绩过一本线。

3. 新加坡
  - 总预算65-80w，对于高考失利，成绩一般或者较弱的学生，通过新加坡私立大学学习，能够逆袭海外名校（QS200以内）

4. 韩国、马来西亚
  - 适合低预算，想出国留学的同学，4年总预算50W以内。既离家不远，又省钱，还有机会申请进QS前200的海外大学
  - 韩国总预算50-60w，每年10w以上，韩语成绩在韩国大学的录取中占有较大的权重，即便高中成绩一般，但是韩语好，仍然有机会申请进QS前100的韩国大学，所以适合不想上专科，英语一般，想拿个本科文凭的人。

5. 英国、澳洲方向
  - 2个国家总预算都要125w多，每年至少30w，热门留学国家，名校多，升学方式多样，预科、国际大一或者高考成绩，可冲击高排名海外大学

6. 日本
  - 总预算40-60，每年15w左右`],
      [UserEducationGroup.JapanHighSchool, `以下是日语高中生升学经常推荐的路径，以推荐的频次作为优先级排序，可以进行参考。但是注意推荐的项目一定要符合客户当前告知你的预算。预算内没有符合路径，就不推荐任何项目。**注意预算要严格按照以下提供的数字进行解答，这对我的工作非常重要**
1. 日本大学直通车1+4
   - 国内指定学校读1年日语，后4年直入日本大学。适合国内日语生或英语较弱、想往日本方向发展的学生。
   - 总预算 75-80w，每年15-20w。最低要求日语达到N5。

2. 国际本科厦门大学2+2-日本方向
   - 厦门大学读2年，后2年在日本静冈产业大学就读，获得日本大学本科学位，管理类学科和管理心理学科。
   - 总预算：50-60w，每年12-15w。免试录取条件：高考总分达本科线且英语/日语单科成绩90分及以上。

3. 南京传媒学院2+2-日本方向
   - 南京传媒学院读2年，后2年对接日本城西国际大学，艺术类留学方向。
   - 总预算 55-65w，每年14-17w。适合国内日语艺术生。

4. 集美大学2+2 日本方向
   - 集美大学读2年，后2年对接日本环太平洋大学，综合经管方向（经济学、管理学等）。
   - 总预算：40-50w，每年10-13w。适合预算有限、向往日本留学的学生。

5. 大连大学2+2/3 日本方向
   - 大连大学读2年，后2-3年根据专业不同，对接日本大学，专业选择包括漫画/动画、国际艺术设计、旅游文化等。
   - 总预算：40-50w，每年10-13w。适合国内日语生。接受高中或中专毕业生，材料审核不合格者不予录取。

6. 日本当地考学
   - 1-2年在日本语言学校学习日语，备考EJU和校内考，考上大学后在日本读4年本科。适合预算和时间充裕、对日本名校有追求的学生。
   - 总预算：80-90w，每年16-20w。适合希望考上排名靠前的日本大学（如早庆上理）。

日语较差或成绩较差推荐 1，2，3，4，5 方向，成绩不错推荐 6 可以冲名校。2，3，4，5 跟 1的区别是想尽快出国，还是缓一年再出国。`],
      [UserEducationGroup.College, `以下是大专生升学经常推荐的路径，以推荐的频次作为优先级排序，可以进行参考。但是注意推荐的项目一定要符合客户当前告知你的预算。预算内没有符合路径，就不推荐任何项目。**注意预算要严格按照以下提供的数字进行解答，这对我的工作非常重要**
1. 韩国3+1中文授课专升本
    - 总预算10-15w，省钱、省时，也省去学语言的精力，离国内近。0语言入读，低预算，1年拿到韩国本科文凭。

2. 新加坡
    - 总预算30-65w，新加坡私立大学升学选择多，升本升硕项目多。既可以申对接的海外名校的本科，也可以选择一步到位升硕士。若去英澳预算不足，那新加坡就是英语系首选。

3. 英国和澳洲方向
    - 英国总预算35-45w，学制一般1年，澳洲80-90w，学制一般2-2.5年。热门留学国家，名校多，澳洲和英国都有对应的专升本项目，拿到英澳大学的本科学位。作为跳板，可申请世界名校硕士。

4. 合作办学方向
    - 学制2年，总预算30w，省钱不出国，可以选择非全日制的学习方式，工作和学历提升两不误，拿海外大学的硕士文凭。

5. 香港方向
    - 学制2年的，总预算30w+，离家近，有中文授课，2年专升本学习，既拿到了本科学位，又可以考虑在当地工作，工作5年之后，拿到香港身份。`],
      [UserEducationGroup.University, `以下是本科生升学经常推荐的路径，以推荐的频次作为优先级排序，可以进行参考。但是注意推荐的项目一定要符合客户当前告知你的预算。预算内没有符合路径，就不推荐任何项目。**注意预算要严格按照以下提供的数字进行解答，这对我的工作非常重要**
1. 英国
    - 学制1年，预算40-60w，偏好国内985、211学生，其次是高分双非，对学术要求相对高。1年制硕士，省时省钱，回国认可度高。

2. 澳洲
    - 每年预算40-55w，学制一般1-2年，宽进严出，澳洲硕士申请门槛低于英美，低分也能逆袭前100，其次澳洲具备移民属性，硕士毕业可留在当地工作拿身份。

3. 香港
    - 每年预算30-50w，香港硕士1年制，省时省钱，认可度高，离家近，毕业之后留港工作，可拿香港身份。

4. 新加坡
    - 每年预算30-35w，学制1-2年，去新加坡读硕士包容性强，学霸可以冲新二；而普娃和成绩较弱的学生，可以冲私立大学的合作办学项目，拿英美澳加的大学的硕士文凭。城市虽小，但是法制严格，留学安全。

5. 美国
    - 每年预算60-100w，1-2年学制，硕士阶段的TOP1，国际学历含金量和认可度高。美研申请不聚焦在学生的学术成绩，更看重学生全面的综合能力，就业机会多。

6. 中外合作办学硕士
    - 总预算15-35w，1-2年学制，省钱不出国，可以选择非全日制的学习方式，工作和学历提升两不误，拿海外大学的硕士文凭。

7. 韩国
    - 总预算10-30w, 1-2年学制，可以半工半读，性价比较高，三种不同语言（中，英，韩语）的硕士授课项目，适合不同语言类型的学生，不会韩语、英语较弱的学生，花1-2年的时间，也可以拿韩国名校的硕士学位`],
      [UserEducationGroup.Gaokao, `以下是高中生升学经常推荐的路径，以推荐的频次作为优先级排序，可以进行参考，但是注意推荐的项目一定要符合客户当前告知你的预算。预算内没有符合路径，就不推荐任何项目。**注意预算要严格按照以下提供的数字进行解答，这对我的工作非常重要**
1. 国际本科3+1/2+2
  - 高考失利和无法上本科的同学的海外升学捷径，对于想体验留学生活的同学，意味着花更少的钱，本科保底，逆袭海外名校
  - 国家本科3+1，总预算50-80w，每年15w以上，分数在本科线下，英语单科在60以上，对接学校一般在QS200-300；2+2的总预算60-80w，成绩一般，英语单科80分上。不想在国内读普通本科的同学。
  
2. 港澳方向
  - 澳门总预算80w+，香港100w+；每年20-35w,“高考+留学”双保险。港澳申请与高考时间不冲突，可提前申请港澳学校，又可参加高考志愿填报，并且港澳离大陆近，对于留学不想去太远的家庭的首选。需要学生成绩过一本线。

3. 新加坡
  - 总预算65-80w，对于高考失利，成绩一般或者较弱的学生，通过新加坡私立大学学习，能够逆袭海外名校（QS200以内）

4. 韩国、马来西亚
  - 适合低预算，想出国留学的同学，4年总预算50W以内。既离家不远，又省钱，还有机会申请进QS前200的海外大学
  - 韩国总预算50-60w，每年10w以上，韩语成绩在韩国大学的录取中占有较大的权重，即便高中成绩一般，但是韩语好，仍然有机会申请进QS前100的韩国大学，所以适合不想上专科，英语一般，想拿个本科文凭的人。

5. 英国、澳洲方向
  - 2个国家总预算都要125w多，每年至少30w，热门留学国家，名校多，升学方式多样，预科、国际大一或者高考成绩，可冲击高排名海外大学

6. 日本
  - 总预算40-60，每年15w左右`]
    ])
  }, 60000)

  // it('512312', async () => {
  //   let chatState = ChatStateStore.get('1123')
  //   console.log(chatState.isInviteToConsultGroup, chatState.isEnterOperationGroup)
  //
  //   ChatStateStore.update('1123', {
  //     isInviteToConsultGroup: true
  //   })
  //
  //   chatState = ChatStateStore.get('1123')
  //   console.log(chatState.isInviteToConsultGroup, chatState.isEnterOperationGroup)
  //
  // }, 60000)

  it('12323', async () => {
    const nextEducationLevel = {
      [UserEducationGroup.JuniorTwoToFour]: '高中',
      [UserEducationGroup.HighSchool]: '本科',
      [UserEducationGroup.College]: '本科',
      [UserEducationGroup.University]: '硕士',
      [UserEducationGroup.Gaokao]: '高中',
      [UserEducationGroup.JapanHighSchool]: '高中'
    }
    console.log(nextEducationLevel['fk'])
  }, 60000)

  it('test', async () => {
    console.log(JSON.stringify(await axios.get('http://112.124.32.162:3001'), null, 4))
  }, 60000)

  it('123', async () => {
    const llmRes =  await new LLM({ model: 'claude-3-5-sonnet-20240620' }).predict('hi')
    console.log(llmRes)
  }, 60000)

  it('test123123', async () => {
    await PrismaMongoClient.getInstance().chat.update({
      where: {
        id: '7881301047907394_1688855135509802'
      },
      data: {
        chat_state: {
          slotAskedCount: {
            preferred_plan: 3,
            none_info: 3,
            school: 1,
            gpa: 1,
            budget_is_per_year: 1
          },
          nodeInvokeCount: {
            IntentionCalculateNode: 10,
            StudyPlanRoutingNode: 1,
            SelfPlanNode: 1,
            SelfPlanConsultNode: 4,
            InviteToConsultantNode: 2,
            InviteToGroupHelper: 1
          },
          nextStage: 'end',
          projects: [],
          pickedMiddleProject: '参考建议：\nExplanation:\n根据您提供的信息，您是一名大二的本科生，预算为30万人民币，目标是出国留学。考虑到您的预算和需求，我建议您可以考虑以下选择：\n\n1. 香港留学：\n香港的硕士项目每年预算在30-50万人民币之间，刚好符合您的预算范围。香港的硕士项目通常为1年制，这意味着您可以在较短时间内完成学业，既省时又省钱。此外，香港的学历在国际上有很高的认可度，地理位置离大陆较近，文化差异相对较小，可能更容易适应。毕业后，您还有机会留在香港工作，获得香港身份。\n\n2. 新加坡留学：\n新加坡的硕士项目每年预算在30-35万人民币左右，也符合您的预算范围。新加坡的教育质量高，国际认可度好，而且是一个安全、法制严格的城市国家，非常适合留学。新加坡的硕士项目通常为1-2年制，您可以根据自己的情况选择合适的课程。此外，新加坡的大学对国际学生较为包容，您有机会申请到知名大学。\n\n3. 韩国留学：\n虽然韩国不在最常推荐的选项中，但考虑到您的预算，韩国可能是一个很好的选择。韩国的硕士项目总预算在10-30万人民币之间，非常符合您的预算要求。韩国提供多种语言的硕士授课项目，包括韩语、英语和中文，您可以根据自己的语言能力选择合适的项目。此外，韩国的留学生活成本相对较低，您可能还有机会半工半读，进一步降低留学成本。\n\n考虑到您的预算和目标，我最推荐的是香港留学。香港的教育质量高，国际认可度好，地理位置优越，而且1年制的硕士项目可以让您更快完成学业，进入职场或继续深造。\n\nRecommended program: 香港留学（硕士项目）',
          userSlots: {
            budget: 30,
            current_level_of_education: '本科',
            grade: '大二',
            goal: '出国留学专业推荐',
            is_study_abroad: 'true',
            is_parent: false,
            is_japanese_student: false,
            is_gaokao_student: false,
            education_group: 3,
            budget_is_per_year: true,
            user_intended_country: [
              '新加坡'
            ],
            major: '市场营销',
            application_stage: '本科',
            city: '青岛',
            school: '青岛大学',
            gpa: '3.8'
          },
          intentions: [
            '1',
            '1',
            '1',
            '1',
            '1'
          ],
          state: 'Consult Group Entered',
          counselor_id: 'HuRong_1',
        }
      }
    })
  }, 60000)

  it('123123', async () => {
    function isCurrentTimeBetweenMidnightAnd5AM (): boolean {
      const now = new Date ()
      const hours = now.getHours ()
      return hours >= 0 && hours < 5
    }

    // 使用示例
    if (isCurrentTimeBetweenMidnightAnd5AM ()) {
      console.log ('当前时间在凌晨 0 点到 5 点之间')
    } else {
      console.log ('当前时间不在凌晨 0 点到 5 点之间')
    }
  }, 60000)

  it('', async () => {
    // 10.1 - 10.13
    // chat
    // state.status.operationGroupEntered
    // 10

    // chatHistory
  }, 60000)
})